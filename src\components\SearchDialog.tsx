import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Search, X, Clock, Filter } from 'lucide-react';
import { SearchItem, SearchCategory, searchCategories, searchByCategory } from '../lib/searchIndex';
import { smartSearchNavigation } from '../utils/searchNavigation';

interface SearchDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenWindow?: (windowId: string, windowName: string) => void;
  onNavigate?: (item: SearchItem) => void;
}

interface SearchHistory {
  query: string;
  timestamp: number;
}

const SearchDialog: React.FC<SearchDialogProps> = ({ 
  isOpen, 
  onClose, 
  onOpenWindow,
  onNavigate 
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Session-based search history (no persistence)
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Cleanup debounce timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Handle result selection
  const handleSelectResult = useCallback((item: SearchItem) => {
    // Use smart navigation to handle different types of search results
    smartSearchNavigation(item, onOpenWindow);
    onClose();
  }, [onOpenWindow, onClose]);

  // Perform search when query or category changes
  useEffect(() => {
    if (query.trim()) {
      const searchResults = searchByCategory(query, selectedCategory);
      setResults(searchResults.slice(0, 10)); // Limit to 10 results
      setSelectedIndex(0);
      setShowHistory(false);
    } else {
      setResults([]);
      setShowHistory(searchHistory.length > 0);
    }
  }, [query, selectedCategory, searchHistory.length]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowDown':
          e.preventDefault();
          if (showHistory) {
            setSelectedIndex(prev => Math.min(prev + 1, searchHistory.length - 1));
          } else {
            setSelectedIndex(prev => Math.min(prev + 1, results.length - 1));
          }
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => Math.max(prev - 1, 0));
          break;
        case 'Enter':
          e.preventDefault();
          if (showHistory && searchHistory[selectedIndex]) {
            setQuery(searchHistory[selectedIndex].query);
            setShowHistory(false);
          } else if (results[selectedIndex]) {
            handleSelectResult(results[selectedIndex]);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, results, selectedIndex, showHistory, searchHistory, onClose, handleSelectResult]);

  // Add to search history (debounced)
  const addToSearchHistory = useCallback((searchQuery: string) => {
    if (searchQuery.trim() && !searchHistory.some(h => h.query === searchQuery.trim())) {
      const newHistory = [
        { query: searchQuery.trim(), timestamp: Date.now() },
        ...searchHistory.slice(0, 9) // Keep only last 10 searches
      ];
      setSearchHistory(newHistory);
    }
  }, [searchHistory]);

  // Debounced function to add to search history
  const debouncedAddToHistory = useCallback((searchQuery: string) => {
    // Clear existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set new timeout to add to history after user stops typing
    debounceTimeoutRef.current = setTimeout(() => {
      addToSearchHistory(searchQuery);
    }, 1000); // 1 second delay
  }, [addToSearchHistory]);

  const handleSearch = (searchQuery: string) => {
    setQuery(searchQuery);

    // Only add to history after user stops typing for 1 second
    if (searchQuery.trim()) {
      debouncedAddToHistory(searchQuery);
    }
  };

  const handleClearHistory = () => {
    setSearchHistory([]);
    setShowHistory(false);
  };
  const getCategoryColor = (categoryId: string): string => {
    const category = searchCategories.find(c => c.id === categoryId);
    return category?.color || 'text-gray-600';
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center animate-in fade-in p-4"
      style={{ zIndex: 10001 }}
      onClick={onClose}
    >
      <div
        className="bg-[#D4E1F4] border-2 border-t-[#769bce] border-l-[#769bce] border-r-[#183969] border-b-[#183969] shadow-2xl w-full max-w-[700px] max-h-[calc(80vh+200px)] rounded-lg overflow-hidden animate-slide-up"
        onClick={(e) => e.stopPropagation()}
        style={{ fontFamily: 'Tahoma, sans-serif' }}
      >
        {/* Title Bar */}
        <div className="bg-gradient-to-r from-[#2155C4] to-[#4D90FE] text-white font-bold text-sm px-3 py-1 flex justify-between items-center cursor-default">
          <div className="flex items-center space-x-2">
            <Search size={16} />
            <span>Search Results</span>
          </div>
          <button 
            onClick={onClose} 
            className="bg-[#E2614A] hover:bg-[#F07A68] border border-[#8C3B2E] text-white w-5 h-5 flex items-center justify-center rounded-sm text-xs font-mono"
          >
            ×
          </button>
        </div>

        {/* Search Input Section */}
        <div className="p-4 border-b border-gray-300">
          <div className="flex items-center space-x-2 mb-3">
            <div className="flex-1 relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
              <input
                ref={inputRef}
                type="text"
                value={query}
                onChange={(e) => handleSearch(e.target.value)}
                placeholder="Search for projects, skills, contact info..."
                className="w-full pl-10 pr-4 py-2 border-2 border-gray-400 rounded text-sm focus:outline-none focus:border-blue-500"
                style={{
                  borderTopColor: '#808080',
                  borderLeftColor: '#808080',
                  borderRightColor: '#ffffff',
                  borderBottomColor: '#ffffff'
                }}
              />
            </div>
            {searchHistory.length > 0 && (
              <button
                onClick={() => setShowHistory(!showHistory)}
                className="p-2 border border-gray-400 rounded hover:bg-gray-100 transition-colors"
                title="Search History"
              >
                <Clock size={16} className="text-gray-600" />
              </button>
            )}
          </div>

          {/* Category Filter */}
          <div className="flex items-center space-x-2 overflow-x-auto">
            <Filter size={14} className="text-gray-600 flex-shrink-0" />
            <div className="flex space-x-1">
              {searchCategories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`px-3 py-1 text-xs rounded border transition-colors whitespace-nowrap ${
                    selectedCategory === category.id
                      ? 'bg-blue-500 text-white border-blue-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100'
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Results Section */}
        <div className="flex-1 overflow-y-auto max-h-[600px]" ref={resultsRef}>
          {showHistory && searchHistory.length > 0 ? (
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-bold text-gray-700">Recent Searches</h3>
                <button
                  onClick={handleClearHistory}
                  className="text-xs text-blue-600 hover:text-blue-800 underline"
                >
                  Clear History
                </button>
              </div>
              <div className="space-y-1">
                {searchHistory.map((historyItem, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setQuery(historyItem.query);
                      setShowHistory(false);
                    }}
                    className={`w-full text-left px-3 py-2 rounded text-sm transition-colors ${
                      selectedIndex === index
                        ? 'bg-blue-500 text-white'
                        : 'hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <Clock size={14} className="text-gray-500" />
                      <span>{historyItem.query}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ) : results.length > 0 ? (
            <div className="p-4">
              <div className="text-xs text-gray-600 mb-3">
                Found {results.length} result{results.length !== 1 ? 's' : ''}
                {selectedCategory !== 'all' && ` in ${searchCategories.find(c => c.id === selectedCategory)?.name}`}
              </div>
              <div className="space-y-1">
                {results.map((item, index) => (
                  <button
                    key={item.id}
                    onClick={() => handleSelectResult(item)}
                    className={`w-full text-left px-3 py-3 rounded border transition-colors ${
                      selectedIndex === index
                        ? 'bg-blue-500 text-white border-blue-600'
                        : 'bg-white hover:bg-gray-50 border-gray-200'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm mb-1">{item.title}</div>
                        <div className={`text-xs mb-1 ${selectedIndex === index ? 'text-blue-100' : 'text-gray-600'}`}>
                          {item.description}
                        </div>
                      </div>
                      <div className={`text-xs px-2 py-1 rounded ${
                        selectedIndex === index
                          ? 'text-white bg-blue-400 bg-opacity-30'
                          : `${getCategoryColor(item.category)} bg-opacity-20`
                      }`}>
                        {item.category}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ) : query.trim() ? (
            <div className="p-8 text-center text-gray-500">
              <Search size={32} className="mx-auto mb-3 text-gray-400" />
              <p className="text-sm">No results found for "{query}"</p>
              <p className="text-xs mt-1">Try different keywords or check a different category</p>
            </div>
          ) : (
            <div className="p-8 text-center text-gray-500">
              <Search size={32} className="mx-auto mb-3 text-gray-400" />
              <p className="text-sm">Start typing to search...</p>
              <p className="text-xs mt-1">Search for projects, skills, contact info, and more</p>
            </div>
          )}
        </div>

        {/* Footer with keyboard shortcuts */}
        <div className="border-t border-gray-300 px-4 py-2 bg-gray-50 text-xs text-gray-600">
          <div className="flex justify-between items-center">
            <span>Use ↑↓ to navigate, Enter to select, Esc to close</span>
            <span>{results.length > 0 && `${selectedIndex + 1} of ${results.length}`}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchDialog;
