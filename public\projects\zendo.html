<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Zendo - Browser Extension</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://rsms.me/inter/inter.css');
        html { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">
    <div class="container mx-auto max-w-4xl py-8 px-4">

        <!-- Header -->
        <header class="text-center mb-12">
            <h1 class="text-5xl font-bold text-gray-900">Zendo Browser Extension</h1>
            <p class="text-xl text-gray-600 mt-2">
                A minimalist to-do list with task grouping, drag-and-drop, and color priorities.
            </p>
        </header>

        <!-- Screenshot -->
        <div class="mb-12">
            <img src="/projects/z-task.png" alt="Zendo Extension Screenshot" class="rounded-lg shadow-2xl mx-auto w-full max-w-2xl">
        </div>

        <!-- CTA -->
        <div class="text-center mb-12">
            <button onclick="openChromeStore()" class="bg-blue-600 text-white font-bold py-3 px-8 rounded-lg shadow-lg hover:bg-blue-700 transition-transform transform hover:scale-105">
                View on Chrome Web Store
            </button>
        </div>

        <div class="grid md:grid-cols-2 gap-8 mb-12">
            <!-- Tech Stack -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h3 class="text-2xl font-bold mb-4">Technology Stack</h3>
                <div class="flex flex-wrap gap-2">
                    <span class="bg-blue-100 text-blue-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded">JavaScript</span>
                    <span class="bg-blue-100 text-blue-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded">Chrome Extension API</span>
                    <span class="bg-blue-100 text-blue-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded">HTML5</span>
                    <span class="bg-blue-100 text-blue-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded">CSS3</span>
                    <span class="bg-blue-100 text-blue-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded">Chrome Sync</span>
                </div>
            </div>

            <!-- Project Status -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h3 class="text-2xl font-bold mb-4">Project Status</h3>
                <ul>
                    <li><strong>Status:</strong> <span class="text-green-600 font-semibold">Published</span></li>
                    <li><strong>Version:</strong> Latest</li>
                    <li><strong>Platform:</strong> Chrome Web Store</li>
                    <li><strong>Offline:</strong> Fully supported</li>
                </ul>
            </div>
        </div>

        <!-- Features -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-12">
            <h3 class="text-2xl font-bold mb-4">Key Features</h3>
            <ul class="list-disc list-inside space-y-2">
                <li>Grouped Tasks - Organize tasks by project, context, or theme</li>
                <li>Drag & Drop - Easily reorder tasks and groups with intuitive gestures</li>
                <li>Custom Priorities - Choose and personalize priority levels using color tags</li>
                <li>Zen-Inspired Design - Clean, minimal UI with Apple-style visual polish</li>
                <li>One-Click Complete & Edit - Click to check off tasks or edit inline</li>
                <li>Chrome Sync - Store tasks and settings across devices</li>
            </ul>
        </div>

        <!-- Description -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-12">
            <h3 class="text-2xl font-bold mb-4">Description</h3>
            <div class="space-y-4 text-gray-700">
                <p><strong>Zendo - Grouped To-Do List & Task Manager for Chrome</strong><br>
                    Zendo is a minimalist to-do list extension for Chrome that helps you organize tasks into groups, set color-coded priorities, and stay focused with a clean, drag-and-drop interface.
                </p>
                <p><strong>Zendo is ideal for:</strong><br>
                    • Daily task planning<br>
                    • Project-based to-do management<br>
                    • Students, creatives, and professionals seeking simplicity<br>
                    • Anyone who prefers grouped over nested tasks
                </p>
                <p>
                    Works fully offline and uses Chrome Sync to store your tasks and settings across devices.<br>
                    <strong>No login. No tracking. No distractions.</strong>
                </p>
            </div>
        </div>
        
        <!-- Alternative Access -->
        <div class="text-center mt-12 p-6 bg-gray-100 rounded-lg">
            <p class="font-semibold mb-4">Alternative ways to access:</p>
            <div class="flex flex-col md:flex-row items-center justify-center gap-4">
                <a href="https://chromewebstore.google.com/detail/zendo/docecfpjajpjlfpidlbmdodgaopdgdfo" onclick="openChromeStore(); return false;" class="text-blue-600 hover:underline">
                    Click to open Chrome Web Store
                </a>
                <div class="flex items-center gap-2">
                    <code id="copy-url" class="bg-white p-2 border rounded-md text-sm text-gray-600 cursor-pointer">
                        https://chromewebstore.google.com/detail/zendo/docecfpjajpjlfpidlbmdodgaopdgdfo
                    </code>
                    <span id="copy-status" class="text-green-600 text-sm"></span>
                </div>
            </div>
        </div>

    </div>

    <script>
      function copyURLToClipboard() {
        const url = document.getElementById("copy-url").innerText;
        navigator.clipboard
          .writeText(url)
          .then(() => {
            const status = document.getElementById("copy-status");
            status.textContent = "Copied!";
            setTimeout(() => {
              status.textContent = "";
            }, 2000);
          })
          .catch((err) => {
            console.error("Copy failed", err);
          });
      }

      function openChromeStore() {
        const url = "https://chromewebstore.google.com/detail/zendo/docecfpjajpjlfpidlbmdodgaopdgdfo";

        try {
          if (window.parent && window.parent !== window) {
            window.parent.postMessage({
              type: 'OPEN_EXTERNAL_URL',
              url: url
            }, '*');
            return;
          }
        } catch (e) {
          console.log("postMessage to parent failed:", e);
        }

        try {
          const newWindow = window.open(url, '_blank', 'noopener,noreferrer');
          if (newWindow) {
            return;
          }
        } catch (e) {
          console.log("window.open failed:", e);
        }

        navigator.clipboard.writeText(url).then(() => {
          alert("Due to browser security restrictions, the URL has been copied to your clipboard.\nPlease paste it in a new browser tab:\n\n" + url);
        }).catch(() => {
          alert("Please copy this URL and open it in a new tab:\n\n" + url);
        });
      }
    </script>
</body>
</html>