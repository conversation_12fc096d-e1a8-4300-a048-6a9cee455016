
import { Folder, FileText } from 'lucide-react';
import FolderWindow from '@shared/components/FolderWindow';
import { FolderItem, DEFAULT_GRID_COLS } from '@shared/types/FolderWindow';

interface MyDocumentsContentProps {
  onOpenWindow: (id: string, name: string) => void;
}

const documents: FolderItem[] = [
  {
    id: 'projects',
    name: 'My Projects',
    icon: <Folder size={48} />,
    type: 'folder',
    color: 'text-yellow-500'
  },
  {
    id: 'my_resume',
    name: 'my_resume.pdf',
    icon: <FileText size={48} />,
    type: 'file',
    color: 'text-red-600'
  },
  {
    id: 'skills',
    name: 'Skills & Experience',
    icon: <Folder size={48} />,
    type: 'folder',
    color: 'text-yellow-500'
  },
  {
    id: 'certifications',
    name: 'Certifications',
    icon: <Folder size={48} />,
    type: 'folder',
    color: 'text-yellow-500'
  },
];

const MyDocumentsContent = ({ onOpenWindow }: MyDocumentsContentProps) => {
  const handleItemDoubleClick = (item: FolderItem) => {
    onOpenWindow(item.id, item.name);
  };

  return (
    <FolderWindow
      title="My Documents"
      icon={<Folder size={16} className="text-blue-600" />}
      items={documents}
      layout="grid"
      gridCols={{
        sm: 2,
        md: 4,
        lg: 4,
        xl: 4
      }}
      onItemDoubleClick={handleItemDoubleClick}
      onOpenWindow={onOpenWindow}
      showSidebar={false}
      variant="default"
    />
  );
};

export default MyDocumentsContent;
