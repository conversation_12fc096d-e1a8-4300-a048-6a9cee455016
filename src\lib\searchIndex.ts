export interface SearchItem {
  id: string;
  title: string;
  description: string;
  category: 'Navigation' | 'Projects' | 'Skills' | 'Contact' | 'Documents' | 'Technology';
  keywords: string[];
  windowId?: string;
  windowName?: string;
  path?: string;
  action?: () => void;
}

export interface SearchCategory {
  id: string;
  name: string;
  color: string;
}

export const searchCategories: SearchCategory[] = [
  { id: 'all', name: 'All Categories', color: 'text-blue-600' },
  { id: 'Navigation', name: 'Navigation', color: 'text-green-600' },
  { id: 'Projects', name: 'Projects', color: 'text-orange-600' },
  { id: 'Skills', name: 'Skills', color: 'text-purple-600' },
  { id: 'Technology', name: 'Technology', color: 'text-blue-500' },
  { id: 'Contact', name: 'Contact', color: 'text-red-600' },
  { id: 'Documents', name: 'Documents', color: 'text-yellow-600' },
];

// Comprehensive searchable content index
export const searchIndex: SearchItem[] = [
  // Navigation Items
  {
    id: 'nav-my-computer',
    title: 'My Computer',
    description: 'System information and technology stack',
    category: 'Navigation',
    keywords: ['computer', 'system', 'technology', 'stack', 'tech', 'specs'],
    windowId: 'my_computer',
    windowName: 'My Computer'
  },
  {
    id: 'nav-projects',
    title: 'My Projects',
    description: 'Browse and explore software development projects',
    category: 'Navigation',
    keywords: ['projects', 'portfolio', 'work', 'development', 'software', 'code'],
    windowId: 'projects',
    windowName: 'My Projects'
  },
  {
    id: 'nav-resume',
    title: 'Resume',
    description: 'Professional resume and curriculum vitae',
    category: 'Documents',
    keywords: ['resume', 'cv', 'curriculum', 'vitae', 'experience', 'professional'],
    windowId: 'my_resume',
    windowName: 'Resume - Mark Jovet Verano'
  },
  {
    id: 'nav-skills',
    title: 'Skills & Experience',
    description: 'Technical skills, programming languages, and professional experience',
    category: 'Navigation',
    keywords: ['skills', 'experience', 'technical', 'programming', 'languages', 'professional'],
    windowId: 'skills',
    windowName: 'Skills & Experience'
  },
  {
    id: 'nav-certifications',
    title: 'Certifications',
    description: 'Professional certifications, courses, and continuous learning achievements',
    category: 'Navigation',
    keywords: ['certifications', 'certificates', 'courses', 'learning', 'achievements', 'professional'],
    windowId: 'certifications',
    windowName: 'Professional Certifications'
  },
  {
    id: 'nav-contact',
    title: 'Contact Info',
    description: 'Get in touch via email, LinkedIn, or other professional channels',
    category: 'Navigation',
    keywords: ['contact', 'email', 'linkedin', 'phone', 'reach', 'touch', 'communication'],
    windowId: 'contact',
    windowName: 'Contact Info'
  },
  {
    id: 'nav-about',
    title: 'About Me',
    description: 'Personal information, background, and professional story',
    category: 'Navigation',
    keywords: ['about', 'personal', 'background', 'story', 'bio', 'information'],
    windowId: 'about',
    windowName: 'About Mark Jovet Verano'
  },
  {
    id: 'nav-control-panel',
    title: 'Control Panel',
    description: 'Portfolio control panel with all sections',
    category: 'Navigation',
    keywords: ['control', 'panel', 'settings', 'configuration', 'portfolio'],
    windowId: 'control_panel',
    windowName: 'Control Panel'
  },

  // Technology Stack Items
  {
    id: 'tech-react',
    title: 'React',
    description: 'JavaScript library for building dynamic user interfaces',
    category: 'Technology',
    keywords: ['react', 'javascript', 'ui', 'interface', 'component', 'frontend'],
    path: 'My Computer → Technology Stack'
  },
  {
    id: 'tech-typescript',
    title: 'TypeScript',
    description: 'Typed superset of JavaScript for robust, scalable applications',
    category: 'Technology',
    keywords: ['typescript', 'javascript', 'typed', 'superset', 'scalable'],
    path: 'My Computer → Technology Stack'
  },
  {
    id: 'tech-csharp',
    title: 'C#',
    description: 'Object-oriented programming language for .NET development',
    category: 'Technology',
    keywords: ['c#', 'csharp', 'dotnet', '.net', 'programming', 'language'],
    path: 'My Computer → Technology Stack'
  },
  {
    id: 'tech-aspnet',
    title: 'ASP.NET',
    description: 'Web application framework for building dynamic web applications',
    category: 'Technology',
    keywords: ['asp.net', 'aspnet', 'web', 'framework', 'application', 'dotnet'],
    path: 'My Computer → Technology Stack'
  },
  {
    id: 'tech-nodejs',
    title: 'Node.js',
    description: 'JavaScript runtime for server-side development',
    category: 'Technology',
    keywords: ['nodejs', 'node', 'javascript', 'server', 'runtime', 'backend'],
    path: 'My Computer → Technology Stack'
  },
  {
    id: 'tech-sql',
    title: 'SQL Databases',
    description: 'MSSQL, MySQL, PostgreSQL, SQLite database technologies',
    category: 'Technology',
    keywords: ['sql', 'database', 'mssql', 'mysql', 'postgresql', 'sqlite', 'data'],
    path: 'My Computer → Technology Stack'
  },
  {
    id: 'tech-angular',
    title: 'Angular',
    description: 'TypeScript-based web application framework',
    category: 'Technology',
    keywords: ['angular', 'typescript', 'framework', 'web', 'application', 'frontend'],
    path: 'My Computer → Technology Stack'
  },
  {
    id: 'tech-docker',
    title: 'Docker',
    description: 'Containerization platform for application deployment',
    category: 'Technology',
    keywords: ['docker', 'container', 'deployment', 'devops', 'platform'],
    path: 'My Computer → Technology Stack'
  },

  // Project Items
  {
    id: 'project-zendo',
    title: 'Zendo',
    description: 'Browser Extension for productivity and focus',
    category: 'Projects',
    keywords: ['zendo', 'browser', 'extension', 'productivity', 'focus', 'chrome'],
    windowId: 'project-zendo',
    windowName: 'Zendo - Browser Extension'
  },
  {
    id: 'project-chromepanion',
    title: 'Chromepanion',
    description: 'Chrome extension companion tool',
    category: 'Projects',
    keywords: ['chromepanion', 'chrome', 'extension', 'companion', 'tool', 'browser'],
    windowId: 'project-chromepanion',
    windowName: 'Chromepanion - Browser Extension'
  },
  {
    id: 'project-portfolio',
    title: 'Windows XP Portfolio',
    description: 'This Windows XP-themed portfolio website',
    category: 'Projects',
    keywords: ['portfolio', 'windows', 'xp', 'website', 'theme', 'nostalgic'],
    path: 'Current Website'
  },
  {
    id: 'project-npm-command-runner',
    title: 'NPM Command Runner',
    description: 'VS Code extension to intelligently manage and run custom npm commands with advanced package.json discovery capabilities',
    category: 'Projects',
    keywords: ['npm', 'command', 'runner', 'vscode', 'extension', 'cli', 'package', 'manager', 'typescript', 'node', 'javascript', 'development', 'tools', 'automation', 'workspace', 'package.json', 'scripts'],
    windowId: 'project-npm-command-runner',
    windowName: 'NPM Command Runner - VS Code Extension'
  },
  {
    id: 'project-moverzz',
    title: 'Mobile App',
    description: 'Work in progress web and mobile app (Android and iOS) for real estate rental management',
    category: 'Projects',
    keywords: ['mobile', 'app', 'android', 'ios', 'real estate', 'rental', 'moverzz', 'web app', 'react native', 'cross platform'],
    windowId: 'project-moverzz',
    windowName: 'a work in progress web and mobile app (android and ios) - for real estate'
  },

  // Skills Categories
  {
    id: 'skill-programming',
    title: 'Programming Languages',
    description: 'C#, VB.NET, JavaScript, TypeScript, T-SQL',
    category: 'Skills',
    keywords: ['programming', 'languages', 'c#', 'vb.net', 'javascript', 'typescript', 'sql'],
    path: 'Skills & Experience → Programming Languages'
  },
  {
    id: 'skill-frontend',
    title: 'Frontend Technologies',
    description: 'React, Angular, jQuery, HTML, CSS',
    category: 'Skills',
    keywords: ['frontend', 'react', 'angular', 'jquery', 'html', 'css', 'ui'],
    path: 'Skills & Experience → Frontend'
  },
  {
    id: 'skill-databases',
    title: 'Database Technologies',
    description: 'MSSQL, MySQL, PostgreSQL, SQLite, CosmosDB, NoSQL',
    category: 'Skills',
    keywords: ['database', 'mssql', 'mysql', 'postgresql', 'sqlite', 'cosmosdb', 'nosql'],
    path: 'Skills & Experience → Databases'
  },
  {
    id: 'skill-tools',
    title: 'Development Tools',
    description: 'Visual Studio, VS Code, DevExpress, Postman, Docker',
    category: 'Skills',
    keywords: ['tools', 'visual studio', 'vscode', 'devexpress', 'postman', 'docker'],
    path: 'Skills & Experience → Tools'
  },

  // Contact Information
  {
    id: 'contact-email',
    title: 'Email',
    description: '<EMAIL>',
    category: 'Contact',
    keywords: ['email', 'markjovetverano', 'gmail', 'contact', 'reach'],
    path: 'Contact Info → Email'
  },
  {
    id: 'contact-linkedin',
    title: 'LinkedIn',
    description: 'linkedin.com/in/markoverano',
    category: 'Contact',
    keywords: ['linkedin', 'markoverano', 'professional', 'network', 'social'],
    path: 'Contact Info → LinkedIn'
  },
  {
    id: 'contact-github',
    title: 'GitHub',
    description: 'github.com/markoverano',
    category: 'Contact',
    keywords: ['github', 'markoverano', 'code', 'repository', 'git'],
    path: 'Contact Info → GitHub'
  },
  {
    id: 'contact-phone',
    title: 'Phone',
    description: '(+63)************',
    category: 'Contact',
    keywords: ['phone', 'mobile', 'call', 'contact', 'number'],
    path: 'Contact Info → Phone'
  },
  {
    id: 'contact-portfolio',
    title: 'Portfolio Website',
    description: 'markoverano.dev',
    category: 'Contact',
    keywords: ['portfolio', 'website', 'markoverano', 'dev', 'domain'],
    path: 'Contact Info → Portfolio'
  },

  // Additional Technology Items
  {
    id: 'tech-vbnet',
    title: 'VB.NET',
    description: 'Visual Basic .NET programming language',
    category: 'Technology',
    keywords: ['vb.net', 'visual basic', 'dotnet', '.net', 'programming'],
    path: 'My Computer → Technology Stack'
  },
  {
    id: 'tech-jquery',
    title: 'jQuery',
    description: 'JavaScript library for DOM manipulation',
    category: 'Technology',
    keywords: ['jquery', 'javascript', 'dom', 'library', 'frontend'],
    path: 'My Computer → Technology Stack'
  },
  {
    id: 'tech-html-css',
    title: 'HTML & CSS',
    description: 'Web markup and styling technologies',
    category: 'Technology',
    keywords: ['html', 'css', 'web', 'markup', 'styling', 'frontend'],
    path: 'My Computer → Technology Stack'
  },
  {
    id: 'tech-git',
    title: 'Git Version Control',
    description: 'GitHub, GitLab, TFS, SVN, Bitbucket',
    category: 'Technology',
    keywords: ['git', 'github', 'gitlab', 'tfs', 'svn', 'bitbucket', 'version control'],
    path: 'My Computer → Technology Stack'
  },

  // Additional Navigation Items
  {
    id: 'nav-my-documents',
    title: 'My Documents',
    description: 'Document folder with portfolio files',
    category: 'Navigation',
    keywords: ['documents', 'files', 'folder', 'my documents'],
    windowId: 'my_documents',
    windowName: 'My Documents'
  },
  {
    id: 'nav-my-pictures',
    title: 'My Pictures',
    description: 'Image gallery and project screenshots',
    category: 'Navigation',
    keywords: ['pictures', 'images', 'gallery', 'screenshots', 'photos'],
    windowId: 'my_pictures',
    windowName: 'My Pictures'
  },
  {
    id: 'nav-recycle-bin',
    title: 'Recycle Bin',
    description: 'Humorous collection of deleted items and developer jokes',
    category: 'Navigation',
    keywords: ['recycle', 'bin', 'trash', 'deleted', 'humor', 'jokes'],
    windowId: 'recyclebin',
    windowName: 'Recycle Bin'
  },

  // Personal Information
  {
    id: 'personal-name',
    title: 'Mark Jovet Verano',
    description: 'Full name and professional identity',
    category: 'Navigation',
    keywords: ['mark', 'jovet', 'verano', 'markoverano', 'name', 'developer'],
    windowId: 'about',
    windowName: 'About Mark Jovet Verano'
  },
  {
    id: 'personal-developer',
    title: 'Software Developer',
    description: 'Professional software developer and programmer',
    category: 'Skills',
    keywords: ['developer', 'programmer', 'software', 'engineer', 'coding'],
    windowId: 'skills',
    windowName: 'Skills & Experience'
  }
];

// Search utility functions
export const fuzzySearch = (query: string, items: SearchItem[]): SearchItem[] => {
  if (!query.trim()) return [];

  const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);

  return items
    .map(item => {
      let score = 0;

      // Exact title match gets highest score
      if (item.title.toLowerCase().includes(query.toLowerCase())) {
        score += 100;
      }

      // Keyword matches
      searchTerms.forEach(term => {
        if (item.title.toLowerCase().includes(term)) {
          score += 50;
        }
        if (item.description.toLowerCase().includes(term)) {
          score += 30;
        }
        item.keywords.forEach(keyword => {
          if (keyword.toLowerCase().includes(term)) {
            score += 20;
          }
        });
      });

      return { item, score };
    })
    .filter(result => result.score > 0)
    .sort((a, b) => b.score - a.score)
    .map(result => result.item);
};

export const searchByCategory = (
  query: string,
  category: string,
  items: SearchItem[] = searchIndex
): SearchItem[] => {
  const filteredItems = category === 'all'
    ? items
    : items.filter(item => item.category === category);

  return fuzzySearch(query, filteredItems);
};

export const getAllCategories = (): string[] => {
  return Array.from(new Set(searchIndex.map(item => item.category)));
};
