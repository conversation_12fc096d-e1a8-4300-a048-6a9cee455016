export interface FileTypeStats {
  extension: string;
  count: number;
  linesOfCode: number;
  percentage: number;
  color: string;
}

export interface CodebaseStats {
  totalFiles: number;
  totalLinesOfCode: number;
  fileTypes: FileTypeStats[];
}

// Windows XP-style colors for different file types
const FILE_TYPE_COLORS = {
  '.tsx': '#4A90E2',     // Blue for React TypeScript
  '.ts': '#2E5BBA',      // Darker blue for TypeScript
  '.css': '#E94B3C',     // Red for CSS
  '.scss': '#CD6799',    // Pink for SCSS
  '.html': '#F5A623',    // Orange for HTML
  '.js': '#F8E71C',      // Yellow for JavaScript
  '.jsx': '#50E3C2',     // Teal for React JavaScript
  '.json': '#7ED321',    // Green for JSON
  '.md': '#9013FE',      // Purple for Markdown
  '.svg': '#FF6B6B',     // Coral for SVG
  '.png': '#4ECDC4',     // Turquoise for images
  '.jpg': '#45B7D1',     // Light blue for images
  '.ico': '#96CEB4',     // Mint for icons
  'other': '#B8B8B8'     // Gray for other files
};

// Mock data for development - in a real implementation, this would scan the actual filesystem
export const getCodebaseStats = (): CodebaseStats => {
  // This is a static analysis based on the current codebase structure
  // In a real implementation, you'd scan the filesystem or use a build tool
  const fileTypes: FileTypeStats[] = [
    {
      extension: '.tsx',
      count: 28,
      linesOfCode: 3420,
      percentage: 52.8,
      color: FILE_TYPE_COLORS['.tsx']
    },
    {
      extension: '.css',
      count: 3,
      linesOfCode: 890,
      percentage: 17.2,
      color: FILE_TYPE_COLORS['.css']
    },
    {
      extension: '.html',
      count: 2,
      linesOfCode: 245,
      percentage: 4.7,
      color: FILE_TYPE_COLORS['.html']
    },
    {
      extension: '.jsx',
      count: 4,
      linesOfCode: 320,
      percentage: 6.2,
      color: FILE_TYPE_COLORS['.jsx']
    },
    {
      extension: '.json',
      count: 8,
      linesOfCode: 450,
      percentage: 8.7,
      color: FILE_TYPE_COLORS['.json']
    },
    {
      extension: 'other',
      count: 5,
      linesOfCode: 195,
      percentage: 10.4,
      color: FILE_TYPE_COLORS['other']
    }
  ];

  const totalFiles = fileTypes.reduce((sum, type) => sum + type.count, 0);
  const totalLinesOfCode = fileTypes.reduce((sum, type) => sum + type.linesOfCode, 0);

  return {
    totalFiles,
    totalLinesOfCode,
    fileTypes
  };
};

export const getProjectTrivia = () => {
  return [
    "Built with Vite for lightning-fast development and optimized production builds",
    "Uses React 18 with TypeScript for type-safe, modern component architecture",
    "Implements authentic Windows XP UI patterns with CSS-based styling",
    "Features comprehensive search functionality with fuzzy matching and smart navigation",
    "Includes responsive design that works on both desktop and mobile devices",
    "Comprehensive SEO optimization for search engine visibility",
    "Uses Tailwind CSS for utility-first styling with custom XP theme extensions",
    "Implements custom window management system with drag, resize, and z-index handling",
    "Features modular component architecture with reusable Windows XP UI elements",
    "Advanced search indexing covers all portfolio content with intelligent categorization"
  ];
};

export const getTechnicalHighlights = () => {
  return {
    architecture: "Single Page Application (SPA)",
    buildSystem: "Vite + TypeScript",
    styling: "Tailwind CSS + Custom XP Theme",
    components: "React 18 with Hooks",
    routing: "React Router v6",
    charts: "Recharts for data visualization",
    icons: "Lucide React icon library",
    deployment: "Static hosting optimized"
  };
};
