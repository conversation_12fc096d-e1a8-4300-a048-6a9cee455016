import React, { useState } from 'react';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { Calendar, MapPin, Building, Users, TrendingUp, Award, Code, Briefcase } from 'lucide-react';
import { xpCareerMilestones } from '../../shared/data/xpAnalyticsData';

interface TimelineTooltipProps {
  milestone: typeof xpCareerMilestones[0];
  isVisible: boolean;
  position: { x: number; y: number };
}

const TimelineTooltip: React.FC<TimelineTooltipProps> = ({ milestone, isVisible, position }) => {
  if (!isVisible) return null;

  return (
    <div
      className="fixed z-50 w-80 p-4 bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 backdrop-blur-xl"
      style={{
        left: position.x,
        top: position.y,
        transform: 'translate(-50%, -100%)',
        marginTop: '-10px'
      }}
    >
      <div className="space-y-3">
        <div className="flex items-center gap-3">
          <div
            className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm"
            style={{ backgroundColor: milestone.companyColor }}
          >
            {milestone.xpIcon}
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white">{milestone.position}</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">{milestone.company}</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="flex items-center gap-1">
            <Calendar className="w-3 h-3 text-gray-500" />
            <span className="text-gray-600 dark:text-gray-400">{milestone.durationMonths} months</span>
          </div>
          <div className="flex items-center gap-1">
            <MapPin className="w-3 h-3 text-gray-500" />
            <span className="text-gray-600 dark:text-gray-400">{milestone.location}</span>
          </div>
          <div className="flex items-center gap-1">
            <Building className="w-3 h-3 text-gray-500" />
            <span className="text-gray-600 dark:text-gray-400">{milestone.companySize}</span>
          </div>
          <div className="flex items-center gap-1">
            <Briefcase className="w-3 h-3 text-gray-500" />
            <span className="text-gray-600 dark:text-gray-400">{milestone.employmentType}</span>
          </div>
        </div>

        <div>
          <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Key Technologies</h4>
          <div className="flex flex-wrap gap-1">
            {milestone.technologiesUsed.slice(0, 6).map((tech, index) => (
              <Badge key={index} variant="secondary" className="text-xs px-2 py-0.5">
                {tech}
              </Badge>
            ))}
            {milestone.technologiesUsed.length > 6 && (
              <Badge variant="outline" className="text-xs px-2 py-0.5">
                +{milestone.technologiesUsed.length - 6}
              </Badge>
            )}
          </div>
        </div>

        <div>
          <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Impact Metrics</h4>
          <div className="flex flex-wrap gap-1">
            {milestone.impactMetrics.slice(0, 3).map((metric, index) => (
              <span
                key={index}
                className="px-2 py-1 rounded text-xs font-medium text-white"
                style={{ backgroundColor: metric.xpColor }}
              >
                {metric.value}{metric.unit}
              </span>
            ))}
          </div>
        </div>

        {/* Key Projects */}
        {milestone.keyProjects && milestone.keyProjects.length > 0 && (
          <div>
            <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Key Projects</h4>
            <div className="space-y-1">
              {milestone.keyProjects.slice(0, 2).map((project, index) => (
                <div key={index} className="text-xs">
                  <p className="font-medium text-gray-800 dark:text-gray-200">{project.name}</p>
                  <p className="text-gray-600 dark:text-gray-400">{project.impact}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Career Progression */}
        <div>
          <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Career Level</h4>
          <div className="flex items-center gap-2">
            <Badge
              variant="outline"
              className="text-xs"
              style={{ borderColor: milestone.companyColor, color: milestone.companyColor }}
            >
              {milestone.careerLevel}
            </Badge>
            <span className="text-xs text-gray-600 dark:text-gray-400">
              {milestone.durationMonths} months
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

const EnhancedCareerTimeline: React.FC = () => {
  const [hoveredMilestone, setHoveredMilestone] = useState<string | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });

  // Sort milestones by start date
  const sortedMilestones = [...xpCareerMilestones].sort((a, b) =>
    new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
  );

  // Calculate timeline positions
  const timelineStart = new Date(sortedMilestones[0].startDate);
  const timelineEnd = new Date();
  const totalDuration = timelineEnd.getTime() - timelineStart.getTime();

  const handleMilestoneHover = (milestoneId: string, event: React.MouseEvent) => {
    setHoveredMilestone(milestoneId);
    const rect = event.currentTarget.getBoundingClientRect();
    setTooltipPosition({
      x: rect.left + rect.width / 2,
      y: rect.top
    });
  };

  const handleMilestoneLeave = () => {
    setHoveredMilestone(null);
  };

  // Create technology adoption timeline data
  const technologyTimeline = sortedMilestones.reduce((acc, milestone) => {
    milestone.technologiesLearned.forEach(tech => {
      if (!acc[tech]) {
        acc[tech] = {
          technology: tech,
          adoptedAt: milestone.company,
          adoptedDate: milestone.startDate,
          color: milestone.companyColor,
          category: milestone.industry
        };
      }
    });
    return acc;
  }, {} as Record<string, { technology: string; adoptedAt: string; adoptedDate: string; color: string; category: string }>);

  const technologyTimelineArray = Object.values(technologyTimeline).sort((a, b) =>
    new Date(a.adoptedDate).getTime() - new Date(b.adoptedDate).getTime()
  );

  return (
    <div className="w-full space-y-12">
      {/* Timeline Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-light text-gray-900 dark:text-white mb-2">
          Career Journey Timeline
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Interactive timeline showcasing {sortedMilestones.length} career milestones spanning {Math.round(totalDuration / (1000 * 60 * 60 * 24 * 365))} years
        </p>
      </div>

      {/* Timeline Container */}
      <div className="relative">
        {/* Timeline Line */}
        <div className="absolute top-16 left-0 right-0 h-1 bg-gradient-to-r from-blue-200 via-green-200 to-orange-200 dark:from-blue-800 dark:via-green-800 dark:to-orange-800 rounded-full"></div>

        {/* Timeline Milestones - Responsive Layout */}
        <div className="relative hidden md:flex justify-between items-start min-h-[200px]">
          {sortedMilestones.map((milestone, index) => {
            const startDate = new Date(milestone.startDate);
            const endDate = new Date(milestone.endDate);
            const position = ((startDate.getTime() - timelineStart.getTime()) / totalDuration) * 100;
            const width = ((endDate.getTime() - startDate.getTime()) / totalDuration) * 100;

            return (
              <div
                key={milestone.id}
                className="absolute flex flex-col items-center"
                style={{ left: `${position}%`, width: `${Math.max(width, 8)}%` }}
                onMouseEnter={(e) => handleMilestoneHover(milestone.id, e)}
                onMouseLeave={handleMilestoneLeave}
              >
                {/* Company Node */}
                <div className="relative z-10 mb-4">
                  <div
                    className="w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl shadow-lg cursor-pointer transform transition-all duration-300 hover:scale-110 hover:shadow-xl"
                    style={{ backgroundColor: milestone.companyColor }}
                  >
                    {milestone.xpIcon}
                  </div>

                  {/* Duration Bar */}
                  <div
                    className="absolute top-full mt-2 h-2 rounded-full opacity-70"
                    style={{
                      backgroundColor: milestone.companyColor,
                      width: '100%',
                      minWidth: '60px'
                    }}
                  ></div>

                  {/* Achievement Badge */}
                  {milestone.impactMetrics.length > 0 && (
                    <div className="absolute -top-2 -right-2">
                      <div className="w-6 h-6 rounded-full bg-yellow-500 flex items-center justify-center shadow-lg">
                        <Award className="w-3 h-3 text-white" />
                      </div>
                    </div>
                  )}
                </div>

                {/* Company Info Card */}
                <Card className="w-52 mt-8 border-0 bg-white/90 dark:bg-white/10 backdrop-blur-xl rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <CardContent className="p-4">
                    <div className="text-center space-y-3">
                      {/* Company Header */}
                      <div className="space-y-1">
                        <h3 className="font-semibold text-sm text-gray-900 dark:text-white">
                          {milestone.position}
                        </h3>
                        <p className="text-xs text-gray-600 dark:text-gray-400 font-medium">
                          {milestone.company}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-500">
                          {milestone.duration}
                        </p>
                      </div>

                      {/* Company Details */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-center gap-2">
                          <Badge
                            variant="outline"
                            className="text-xs"
                            style={{ borderColor: milestone.companyColor, color: milestone.companyColor }}
                          >
                            {milestone.careerLevel}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {milestone.employmentType}
                          </Badge>
                        </div>

                        <div className="text-xs text-gray-600 dark:text-gray-400">
                          <div className="flex items-center justify-center gap-1 mb-1">
                            <Building className="w-3 h-3" />
                            <span>{milestone.industry}</span>
                          </div>
                          <div className="flex items-center justify-center gap-1">
                            <Users className="w-3 h-3" />
                            <span>{milestone.companySize}</span>
                          </div>
                        </div>
                      </div>

                      {/* Key Technologies Learned */}
                      <div>
                        <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">New Technologies</h4>
                        <div className="flex flex-wrap gap-1 justify-center">
                          {milestone.technologiesLearned.slice(0, 3).map((tech, i) => (
                            <Badge key={i} variant="secondary" className="text-xs px-1.5 py-0.5">
                              {tech}
                            </Badge>
                          ))}
                          {milestone.technologiesLearned.length > 3 && (
                            <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                              +{milestone.technologiesLearned.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Top Impact Metric */}
                      {milestone.impactMetrics.length > 0 && (
                        <div>
                          <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Key Achievement</h4>
                          <span
                            className="px-2 py-1 rounded text-xs font-medium text-white"
                            style={{ backgroundColor: milestone.impactMetrics[0].xpColor }}
                          >
                            {milestone.impactMetrics[0].metric}: {milestone.impactMetrics[0].value}{milestone.impactMetrics[0].unit}
                          </span>
                        </div>
                      )}

                      {/* Career Progression Indicator */}
                      <div className="flex items-center justify-center gap-1 pt-2">
                        {Array.from({ length: 5 }, (_, i) => (
                          <div
                            key={i}
                            className={`w-2 h-2 rounded-full ${i < (milestone.careerLevel === 'Senior' ? 4 : milestone.careerLevel === 'Mid-level to Senior' ? 3 : 2)
                              ? 'opacity-100'
                              : 'opacity-30'
                              }`}
                            style={{ backgroundColor: milestone.companyColor }}
                          />
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            );
          })}
        </div>

        {/* Timeline Labels */}
        <div className="hidden md:flex justify-between mt-8 text-sm text-gray-500 dark:text-gray-400">
          <span>{timelineStart.getFullYear()}</span>
          <span>Present</span>
        </div>

        {/* Mobile Timeline - Vertical Layout */}
        <div className="md:hidden space-y-6 mt-8">
          {sortedMilestones.map((milestone, index) => (
            <div key={milestone.id} className="relative flex gap-4">
              {/* Vertical Line */}
              {index < sortedMilestones.length - 1 && (
                <div className="absolute left-6 top-12 bottom-0 w-0.5 bg-gray-300 dark:bg-gray-600"></div>
              )}

              {/* Company Node */}
              <div className="flex-shrink-0">
                <div
                  className="w-12 h-12 rounded-full flex items-center justify-center text-white text-lg shadow-lg relative"
                  style={{ backgroundColor: milestone.companyColor }}
                >
                  {milestone.xpIcon}
                  {milestone.impactMetrics.length > 0 && (
                    <div className="absolute -top-1 -right-1">
                      <div className="w-4 h-4 rounded-full bg-yellow-500 flex items-center justify-center">
                        <Award className="w-2 h-2 text-white" />
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Company Info */}
              <Card className="flex-1 border-0 bg-white/90 dark:bg-white/10 backdrop-blur-xl rounded-xl shadow-lg">
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <div>
                      <h3 className="font-semibold text-sm text-gray-900 dark:text-white">
                        {milestone.position}
                      </h3>
                      <p className="text-xs text-gray-600 dark:text-gray-400 font-medium">
                        {milestone.company}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-500">
                        {milestone.duration}
                      </p>
                    </div>

                    <div className="flex flex-wrap gap-1">
                      <Badge
                        variant="outline"
                        className="text-xs"
                        style={{ borderColor: milestone.companyColor, color: milestone.companyColor }}
                      >
                        {milestone.careerLevel}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {milestone.employmentType}
                      </Badge>
                    </div>

                    <div className="flex flex-wrap gap-1">
                      {milestone.technologiesLearned.slice(0, 3).map((tech, i) => (
                        <Badge key={i} variant="secondary" className="text-xs px-1.5 py-0.5">
                          {tech}
                        </Badge>
                      ))}
                    </div>

                    {milestone.impactMetrics.length > 0 && (
                      <span
                        className="inline-block px-2 py-1 rounded text-xs font-medium text-white"
                        style={{ backgroundColor: milestone.impactMetrics[0].xpColor }}
                      >
                        {milestone.impactMetrics[0].metric}: {milestone.impactMetrics[0].value}{milestone.impactMetrics[0].unit}
                      </span>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>

      {/* Technology Adoption Timeline */}
      <div className="mt-16">
        <div className="mb-6">
          <h3 className="text-xl font-light text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <Code className="w-5 h-5" />
            Technology Adoption Journey
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Key technologies learned throughout career progression ({technologyTimelineArray.length} technologies)
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {technologyTimelineArray.map((tech, index) => (
            <Card key={tech.technology} className="border-0 bg-white/90 dark:bg-white/10 backdrop-blur-xl rounded-lg shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div
                    className="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-bold"
                    style={{ backgroundColor: tech.color }}
                  >
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-sm text-gray-900 dark:text-white">
                      {tech.technology}
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Adopted at {tech.adoptedAt}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-500">
                      {tech.category}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Tooltip */}
      {hoveredMilestone && (
        <TimelineTooltip
          milestone={sortedMilestones.find(m => m.id === hoveredMilestone)!}
          isVisible={!!hoveredMilestone}
          position={tooltipPosition}
        />
      )}
    </div>
  );
};

export default EnhancedCareerTimeline;
