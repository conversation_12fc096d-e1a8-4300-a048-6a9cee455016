import { useState, useEffect, useCallback, useRef } from 'react';
import { useIsMobile } from './use-mobile';

export interface NavigationState {
  id: string;
  type: 'desktop' | 'window' | 'dialog';
  windowId?: string;
  windowName?: string;
  dialogType?: string;
  timestamp: number;
}

export interface MobileNavigationManager {
  navigationHistory: NavigationState[];
  currentState: NavigationState | null;
  canGoBack: boolean;
  pushState: (state: Omit<NavigationState, 'id' | 'timestamp'>) => void;
  goBack: () => boolean;
  clearHistory: () => void;
  replaceCurrentState: (state: Omit<NavigationState, 'id' | 'timestamp'>) => void;
  isAtHome: boolean;
}

const DESKTOP_STATE: NavigationState = {
  id: 'desktop-home',
  type: 'desktop',
  timestamp: Date.now()
};

export const useMobileNavigation = (): MobileNavigationManager => {
  const isMobile = useIsMobile();
  const [navigationHistory, setNavigationHistory] = useState<NavigationState[]>([DESKTOP_STATE]);
  const [currentState, setCurrentState] = useState<NavigationState | null>(DESKTOP_STATE);
  const isInitializedRef = useRef(false);

  // Initialize with desktop state
  useEffect(() => {
    if (!isInitializedRef.current && isMobile) {
      isInitializedRef.current = true;
      // Ensure we start with a clean desktop state
      setNavigationHistory([DESKTOP_STATE]);
      setCurrentState(DESKTOP_STATE);
    }
  }, [isMobile]);

  const pushState = useCallback((state: Omit<NavigationState, 'id' | 'timestamp'>) => {
    if (!isMobile) return;

    const newState: NavigationState = {
      ...state,
      id: `${state.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now()
    };

    setNavigationHistory(prev => {
      // Limit history to prevent memory issues (keep last 50 states)
      const newHistory = [...prev, newState];
      return newHistory.length > 50 ? newHistory.slice(-50) : newHistory;
    });
    setCurrentState(newState);
  }, [isMobile]);

  const goBack = useCallback((): boolean => {
    if (!isMobile || navigationHistory.length <= 1) {
      return false;
    }

    setNavigationHistory(prev => {
      const newHistory = prev.slice(0, -1);
      const previousState = newHistory[newHistory.length - 1];
      setCurrentState(previousState);
      return newHistory;
    });

    return true;
  }, [isMobile, navigationHistory.length]);

  const clearHistory = useCallback(() => {
    if (!isMobile) return;
    
    setNavigationHistory([DESKTOP_STATE]);
    setCurrentState(DESKTOP_STATE);
  }, [isMobile]);

  const replaceCurrentState = useCallback((state: Omit<NavigationState, 'id' | 'timestamp'>) => {
    if (!isMobile) return;

    const newState: NavigationState = {
      ...state,
      id: `${state.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now()
    };

    setNavigationHistory(prev => {
      const newHistory = [...prev];
      if (newHistory.length > 0) {
        newHistory[newHistory.length - 1] = newState;
      } else {
        newHistory.push(newState);
      }
      return newHistory;
    });
    setCurrentState(newState);
  }, [isMobile]);

  const canGoBack = navigationHistory.length > 1;
  const isAtHome = currentState?.type === 'desktop' && navigationHistory.length === 1;

  return {
    navigationHistory,
    currentState,
    canGoBack,
    pushState,
    goBack,
    clearHistory,
    replaceCurrentState,
    isAtHome
  };
};
