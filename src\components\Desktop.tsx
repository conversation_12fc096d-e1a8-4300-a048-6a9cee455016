
import { useState, useCallback, useEffect } from 'react';
import Taskbar from './Taskbar';
import DesktopIcon from './DesktopIcon';
import Window from './Window';
import StartMenu from './StartMenu';
import ShutdownSequence from './ShutdownSequence';
import WindowsNotification from './WindowsNotification';
import DialogManager from './DialogManager';
import { useShutdown } from '../hooks/useShutdown';
import { desktopIcons, portfolioItems } from '../lib/constants';
import { useIsMobile } from '../hooks/use-mobile';
import { useWindowManager } from '../hooks/useWindowManager';
import { useMobileNavigationContext } from '../contexts/MobileNavigationContext';
import { useBrowserHistoryIntegration } from '../hooks/useBrowserHistoryIntegration';
import MobileBackButton from './MobileBackButton';
import MobileNavigationDebug from './MobileNavigationDebug';
import {
  getIconPosition,
  initializeIconPositions,
  IconPosition
} from '../utils/iconPositionManager';

import windowsXpBliss from '../assets/windows-xp-bliss.jpg';

const Desktop = () => {
  const [showStartMenu, setShowStartMenu] = useState(false);
  const [notification, setNotification] = useState<{
    title: string;
    message: string;
    icon?: string;
  } | null>(null);
  const [iconPositions, setIconPositions] = useState<Record<string, IconPosition>>({});

  const { isShuttingDown, shutdownType, triggerShutdown, resetToDesktop } = useShutdown();
  const isMobile = useIsMobile();

  const {
    openWindows,
    openWindow,
    closeWindow,
    minimizeWindow,
    maximizeWindow,
    restoreWindow,
    bringToFront,
    updateWindowPosition,
    updateWindowSize,
    handleTaskbarClick,
    handleNavigationBack,
  } = useWindowManager(isMobile);

  // Mobile navigation integration
  const mobileNavigation = useMobileNavigationContext();

  // Browser history integration for mobile back button
  const { triggerBackNavigation } = useBrowserHistoryIntegration({
    navigationManager: mobileNavigation,
    onNavigateBack: (previousState) => {
      if (previousState) {
        handleNavigationBack(previousState);
      }
    },
    onNavigateToHome: () => {
      // Close all windows and return to desktop
      openWindows.forEach(window => closeWindow(window.id));
    },
    onAppExit: () => {
      // Allow the app to close naturally
      console.log('Mobile back button: Allowing app to close');
    }
  });

  const handleOpenWindow = useCallback((iconId: string, iconName: string) => {
    openWindow(iconId, iconName);

    if (iconId === 'projects') {
      setNotification({
        title: 'Projects Tip',
        message: 'Click on any project folder to explore detailed information',
        icon: '💡'
      });
    }
  }, [openWindow]);

  // Initialize icon positions on mount
  useEffect(() => {
    const iconIds = desktopIcons.map(icon => icon.id);
    initializeIconPositions(iconIds);

    // Load current positions
    const positions: Record<string, IconPosition> = {};
    iconIds.forEach(iconId => {
      positions[iconId] = getIconPosition(iconId);
    });
    setIconPositions(positions);
  }, []);

  // Handle icon position updates
  const handleIconPositionUpdate = useCallback((iconId: string, position: IconPosition) => {
    setIconPositions(prev => ({
      ...prev,
      [iconId]: position,
    }));
  }, []);

  // Listen for custom events from welcome dialog
  useEffect(() => {
    const handleOpenWindowEvent = (event: CustomEvent) => {
      const { iconId, iconName } = event.detail;
      handleOpenWindow(iconId, iconName);
    };

    window.addEventListener('openWindow', handleOpenWindowEvent as EventListener);

    return () => {
      window.removeEventListener('openWindow', handleOpenWindowEvent as EventListener);
    };
  }, [handleOpenWindow]);



  if (isShuttingDown) {
    return <ShutdownSequence type={shutdownType} onComplete={resetToDesktop} />;
  }

  // Check if any icon is being dragged for global cursor
  const isAnyIconDragging = Object.values(iconPositions).length > 0 &&
    desktopIcons.some(icon => {
      // This will be updated by the drag hook, but for now we'll handle it in the icon component
      return false;
    });

  return (
    <div
      className="h-screen w-screen relative bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: `url(${windowsXpBliss})`,
        cursor: isAnyIconDragging ? 'grabbing' : 'default',
      }}
      onClick={() => setShowStartMenu(false)}
    >
      {/* Desktop Icons */}
      <div className="absolute top-0 left-0 right-0 bottom-10 overflow-hidden">
        {desktopIcons.map((icon) => {
          const position = iconPositions[icon.id] || { x: 16, y: 16 };
          return (
            <DesktopIcon
              key={icon.id}
              icon={icon}
              position={position}
              allIconIds={desktopIcons.map(i => i.id)}
              onDoubleClick={() => handleOpenWindow(icon.id, icon.name)}
              onPositionUpdate={handleIconPositionUpdate}
            />
          );
        })}
      </div>

      {/* Windows */}
      {openWindows.map((window) => (
        !window.isMinimized && (
          <Window
            key={window.id}
            window={window}
            onClose={() => closeWindow(window.id)}
            onMinimize={() => minimizeWindow(window.id)}
            onMaximize={() => maximizeWindow(window.id)}
            onRestore={() => restoreWindow(window.id)}
            onBringToFront={() => bringToFront(window.id)}
            onUpdatePosition={(position) => updateWindowPosition(window.id, position)}
            onUpdateSize={(size) => updateWindowSize(window.id, size)}
            isMobile={isMobile}
          />
        )
      ))}

      {/* Dialog Manager - handles all dialogs including welcome */}
      <DialogManager />

      {/* Start Menu */}
      {showStartMenu && (
        <StartMenu
          menuItems={portfolioItems}
          onClose={() => setShowStartMenu(false)}
          onOpenWindow={handleOpenWindow}
          onShutdown={triggerShutdown}
        />
      )}

      {/* Taskbar */}
      <Taskbar
        openWindows={openWindows}
        onStartClick={() => setShowStartMenu(!showStartMenu)}
        onOpenWindow={handleOpenWindow}
        onWindowClick={handleTaskbarClick}
      />

      {/* Windows Notification */}
      {notification && (
        <WindowsNotification
          title={notification.title}
          message={notification.message}
          icon={notification.icon}
          onClose={() => setNotification(null)}
        />
      )}

      {/* Mobile Back Button for testing and accessibility */}
      <MobileBackButton onBackClick={triggerBackNavigation} />

      {/* Mobile Navigation Debug Panel */}
      <MobileNavigationDebug />
    </div>
  );
};

export default Desktop;
