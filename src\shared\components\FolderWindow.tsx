import React from 'react';
import { cn } from '@shared/utils';
import WindowsFolderHeader from '../../components/windows/WindowsFolderHeader';
import { 
  FolderWindowProps, 
  FolderItem, 
  SidebarConfig, 
  DEFAULT_GRID_COLS 
} from '@shared/types/FolderWindow';

/**
 * Renders the sidebar for folder windows
 */
const FolderSidebar: React.FC<{
  config: SidebarConfig;
  className?: string;
}> = ({ config, className }) => {
  return (
    <div className={cn(
      "w-64 bg-gradient-to-b from-blue-50 to-blue-100 border-r border-gray-300 p-4 hidden lg:block flex-shrink-0",
      config.className,
      className
    )}>
      {config.sections.map((section, sectionIndex) => (
        <div key={sectionIndex} className="mb-6">
          <h3 className="font-bold text-blue-800 mb-3 text-sm">{section.title}</h3>
          <div className="space-y-2">
            {section.buttons.map((button, buttonIndex) => (
              <button
                key={buttonIndex}
                onClick={button.onClick}
                className="w-full text-left text-sm text-blue-600 hover:text-blue-800 hover:underline cursor-pointer"
              >
                {button.icon && <span className="mr-1">{button.icon}</span>}
                {button.label}
              </button>
            ))}
          </div>
        </div>
      ))}
      
      {config.details && (
        <div>
          <h3 className="font-bold text-blue-800 mb-3 text-sm">Details</h3>
          <div className="bg-white border border-gray-300 p-3 rounded text-xs">
            <p className="text-gray-700">
              <strong>{config.details.title}</strong><br/>
              {config.details.description}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Renders folder items in a grid layout
 */
const GridLayout: React.FC<{
  items: FolderItem[];
  onItemClick?: (item: FolderItem) => void;
  onItemDoubleClick?: (item: FolderItem) => void;
  gridCols?: FolderWindowProps['gridCols'];
  className?: string;
}> = ({ items, onItemClick, onItemDoubleClick, gridCols = DEFAULT_GRID_COLS.default, className }) => {
  // Generate grid classes based on the configuration
  const getGridClasses = () => {
    const baseClasses = "grid gap-4";
    const smCols = gridCols.sm || 2;
    const mdCols = gridCols.md || 3;
    const lgCols = gridCols.lg || 4;
    const xlCols = gridCols.xl || 4;

    // Use explicit classes to ensure they're included in Tailwind build
    const smClass = smCols === 1 ? 'grid-cols-1' :
                   smCols === 2 ? 'grid-cols-2' :
                   smCols === 3 ? 'grid-cols-3' :
                   smCols === 4 ? 'grid-cols-4' :
                   smCols === 5 ? 'grid-cols-5' :
                   smCols === 6 ? 'grid-cols-6' : 'grid-cols-2';

    const mdClass = mdCols === 1 ? 'md:grid-cols-1' :
                   mdCols === 2 ? 'md:grid-cols-2' :
                   mdCols === 3 ? 'md:grid-cols-3' :
                   mdCols === 4 ? 'md:grid-cols-4' :
                   mdCols === 5 ? 'md:grid-cols-5' :
                   mdCols === 6 ? 'md:grid-cols-6' : 'md:grid-cols-3';

    const lgClass = lgCols === 1 ? 'lg:grid-cols-1' :
                   lgCols === 2 ? 'lg:grid-cols-2' :
                   lgCols === 3 ? 'lg:grid-cols-3' :
                   lgCols === 4 ? 'lg:grid-cols-4' :
                   lgCols === 5 ? 'lg:grid-cols-5' :
                   lgCols === 6 ? 'lg:grid-cols-6' : 'lg:grid-cols-4';

    const xlClass = xlCols === 1 ? 'xl:grid-cols-1' :
                   xlCols === 2 ? 'xl:grid-cols-2' :
                   xlCols === 3 ? 'xl:grid-cols-3' :
                   xlCols === 4 ? 'xl:grid-cols-4' :
                   xlCols === 5 ? 'xl:grid-cols-5' :
                   xlCols === 6 ? 'xl:grid-cols-6' : 'xl:grid-cols-4';

    return cn(baseClasses, smClass, mdClass, lgClass, xlClass, className);
  };

  const gridClasses = getGridClasses();

  return (
    <div className={gridClasses}>
      {items.map((item) => {
        const IconComponent = typeof item.icon === 'function' ? item.icon : null;
        
        const handleClick = () => {
          if (item.onClick) {
            item.onClick();
          } else if (onItemClick) {
            onItemClick(item);
          }
        };

        const handleDoubleClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          if (item.onDoubleClick) {
            item.onDoubleClick();
          } else if (onItemDoubleClick) {
            onItemDoubleClick(item);
          }
        };

        return (
          <div
            key={item.id}
            className="flex flex-col items-center justify-center text-center cursor-pointer p-2 rounded hover:bg-blue-100 transition-colors"
            onClick={handleClick}
            onDoubleClick={handleDoubleClick}
          >
            {IconComponent ? (
              <IconComponent
                size={48}
                className={cn(
                  item.color || (item.type === 'folder' ? 'text-yellow-500' : 'text-gray-700'),
                  item.type === 'image' && 'w-full h-full object-cover'
                )}
              />
            ) : (
              <div className={cn(
                "w-12 h-12 flex items-center justify-center",
                item.color || (item.type === 'folder' ? 'text-yellow-500' : 'text-gray-700')
              )}>
                {item.icon}
              </div>
            )}
            <span className="mt-2 text-xs break-words w-full">{item.name}</span>
            {item.size && (
              <span className="text-xs text-gray-500">{item.size}</span>
            )}
          </div>
        );
      })}
    </div>
  );
};

/**
 * Generic folder window component that can be used for different folder types
 */
const FolderWindow: React.FC<FolderWindowProps> = ({
  title,
  icon,
  subtitle,
  items = [],
  layout = 'grid',
  customContent,
  contentRenderer,
  sidebarConfig,
  showSidebar = true,
  onItemClick,
  onItemDoubleClick,
  onOpenWindow,
  showNavigationButtons = true,
  showToolbar = true,
  showAddressBar = true,
  onBackClick,
  onForwardClick,
  onUpClick,
  onSearchClick,
  onFoldersClick,
  customActions,
  variant = 'default',
  className,
  contentClassName,
  gridCols
}) => {
  // Default handlers
  const defaultBackClick = () => console.log('Navigate back');
  const defaultFoldersClick = () => console.log('Toggle folders panel');

  return (
    <div className={cn("bg-white font-tahoma h-full flex flex-col", className)}>
      <WindowsFolderHeader
        title={title}
        subtitle={subtitle}
        icon={icon}
        variant={variant}
        onBackClick={onBackClick || defaultBackClick}
        onForwardClick={onForwardClick}
        onUpClick={onUpClick}
        onSearchClick={onSearchClick}
        onFoldersClick={onFoldersClick || defaultFoldersClick}
        onOpenWindow={onOpenWindow}
        showNavigationButtons={showNavigationButtons}
        showToolbar={showToolbar}
        showAddressBar={showAddressBar}
        customActions={customActions}
      />
      
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        {showSidebar && sidebarConfig && (
          <FolderSidebar config={sidebarConfig} />
        )}
        
        {/* Main Content Area */}
        <div className={cn("flex-1 p-4 overflow-y-auto", contentClassName)}>
          {customContent ? (
            customContent
          ) : contentRenderer ? (
            contentRenderer(items)
          ) : layout === 'grid' ? (
            <GridLayout 
              items={items}
              onItemClick={onItemClick}
              onItemDoubleClick={onItemDoubleClick}
              gridCols={gridCols}
            />
          ) : (
            <div className="text-gray-500">Unsupported layout type</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FolderWindow;
