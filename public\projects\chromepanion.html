<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Chromepanion - Browser Extension</title>
    <style>
      body {
        font-family: "Lucida Grande", "Trebuchet MS", Verdana, sans-serif;
        margin: 0;
        padding: 0;
        background: linear-gradient(
          135deg,
          #f0f4f8 0%,
          #e8f0fe 50%,
          #f8fafc 100%
        );
        color: #2d3748;
        min-height: 100vh;
        line-height: 1.6;
      }
      .container {
        max-width: 900px;
        margin: 0 auto;
        background: #ffffff;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-top: 20px;
        margin-bottom: 20px;
      }
      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px 30px;
        text-align: center;
        position: relative;
      }
      .header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
      }
      h1 {
        font-size: 2.5em;
        margin: 0 0 10px 0;
        font-weight: 300;
        letter-spacing: -1px;
        position: relative;
        z-index: 1;
      }
      .tagline {
        font-size: 1.1em;
        opacity: 0.9;
        font-weight: 300;
        position: relative;
        z-index: 1;
      }
      .content {
        padding: 30px;
      }
      .project-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 30px;
      }
      .info-card {
        background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
        padding: 25px;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        flex: 1;
        min-width: 280px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      }
      .tech-stack {
        margin-top: 15px;
      }
      .tech-tag {
        display: inline-block;
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        color: white;
        padding: 6px 12px;
        margin: 3px;
        font-size: 0.85em;
        border-radius: 15px;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border: none;
      }
      .features {
        list-style-type: none;
        padding-left: 0;
        margin: 0;
      }
      .features li {
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        margin: 12px 0;
        padding: 15px 15px 15px 45px;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
        position: relative;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
      }
      .features li:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      .features li:before {
        content: "✓";
        position: absolute;
        left: 15px;
        top: 15px;
        color: #48bb78;
        font-weight: bold;
        font-size: 1.1em;
      }
      .screenshot-container {
        text-align: center;
        margin: 30px 0;
        padding: 20px;
        background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 8px;
        border: 1px solid #e2e8f0;
      }
      .screenshot-container img {
        max-width: 100%;
        height: auto;
        border-radius: 6px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }
      .action-buttons {
        text-align: center;
        margin: 30px 0;
        padding: 20px;
      }
      .chrome-store-btn {
        display: inline-block;
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        color: white !important;
        padding: 15px 30px;
        text-decoration: none !important;
        font-weight: 600;
        border-radius: 8px;
        font-size: 1.1em;
        box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
        position: relative;
        z-index: 10;
        font-family: inherit;
      }
      .chrome-store-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(66, 153, 225, 0.4);
        background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
        color: white !important;
        text-decoration: none !important;
      }
      .chrome-store-btn:visited {
        color: white !important;
      }
      .chrome-store-btn:active {
        transform: translateY(0);
      }
      h3 {
        color: #2d3748;
        font-size: 1.3em;
        margin-bottom: 15px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      .description-section {
        background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
        padding: 25px;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        margin: 20px 0;
      }
      .description-section p {
        margin: 15px 0;
        line-height: 1.7;
      }
      .description-section strong {
        color: #2d3748;
        font-weight: 600;
      }

      /* Additional button styles for better click handling */
      .chrome-store-btn::before {
        content: "";
        position: absolute;
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        background: transparent;
        z-index: -1;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Chromepanion Browser Extension</h1>
        <p class="tagline">
          Your ultimate companion for browsing with a layer of privacy..
        </p>
      </div>

      <div class="content">
        <!-- Screenshot Section -->
        <div class="screenshot-container">
          <img
            src="/chromepanion.png"
            alt="Chromepanion Extension Screenshot"
          />
        </div>
         <div class="screenshot-container">
          <img
            src="/chromepanion-b.png"
            alt="Chromepanion Extension Screenshot"
          />
        </div>
        <div class="project-info">
          <div class="info-card">
            <h3>Technology Stack</h3>
            <div class="tech-stack">
              <span class="tech-tag">JavaScript</span>
              <span class="tech-tag">Chrome Extension API</span>
              <span class="tech-tag">HTML5</span>
              <span class="tech-tag">CSS3</span>
              <span class="tech-tag">React</span>
            </div>
          </div>

          <div class="info-card">
            <h3>Project Status</h3>
            <p><strong>Status:</strong> Published</p>
            <p><strong>Version:</strong> Latest</p>
            <p><strong>Platform:</strong> Chrome Web Store</p>
            <p><strong>Offline:</strong> Fully supported</p>
          </div>
        </div>

        <!-- Chrome Web Store Link - Simple Direct Approach -->
        <div class="action-buttons">
          <button
            class="chrome-store-btn"
            onclick="openChromeStore()"
            style="border: none; cursor: pointer;"
          >
            View on Chrome Web Store
          </button>
        </div>

        <!-- Alternative Methods - Try These -->
        <div
          style="
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: #f0f4f8;
            border-radius: 6px;
          "
        >
          <p style="margin: 10px 0; font-weight: 600">
            Alternative ways to access:
          </p>

          <!-- Method 1: Right-click friendly link -->
          <p style="margin: 10px 0">
            <a
              href="https://chromewebstore.google.com/detail/chromepanion/kldldhpebabajnikgoecnifblglhoegh?authuser=2&hl=en"
              style="
                color: #3182ce;
                text-decoration: underline;
                font-weight: 500;
              "
              oncontextmenu="return true;"
              onclick="openChromeStore(); return false;"
            >
              Click to open Chrome Web Store
            </a>
          </p>

          <!-- Method 2: Copy-paste URL -->
          <p style="margin: 10px 0; font-size: 0.9em">
            <strong>Copy this URL:</strong><br />
            <code
              id="copy-url"
              style="
                background: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 0.8em;
                word-break: break-all;
                cursor: pointer;
              "
              onclick="copyURLToClipboard()"
              title="Click to copy"
            >
              https://chromewebstore.google.com/detail/chromepanion/kldldhpebabajnikgoecnifblglhoegh?authuser=2&hl=en
            </code>
            <span
              id="copy-status"
              style="margin-left: 10px; font-size: 0.8em; color: green"
            ></span>
          </p>

          <!-- Method 3: Search instructions -->
          <p style="margin: 10px 0; font-size: 0.9em; color: #666">
            <strong>Or search:</strong> "Chromepanion Browser Extension" in Chrome Web Store
          </p>
        </div>

        <div class="info-card">
          <h3>Privacy-First Design</h3>
          <ul>
            <li>
              100% Local Processing: All AI processing happens locally via
              Ollama
            </li>
            <li>
              No External AI Services: No data sent to OpenAI, Claude, or other
              cloud providers
            </li>
            <li>
              Private Search: Your search queries and results stay on your
              device
            </li>
          </ul>

          <h3>AI-Powered Analysis</h3>
          <ul>
            <li>
              10 Specialized Personas: Each with unique analysis styles and
              expertise
            </li>
            <ul>
              <li>Scholar: Academic, research-focused analysis</li>
              <li>Executive: Business and strategic insights</li>
              <li>Storyteller: Narrative and creative perspectives</li>
              <li>Skeptic: Critical thinking and fact-checking</li>
              <li>Mentor: Guidance and educational approach</li>
              <li>Investigator: Deep research and fact-finding</li>
              <li>Pragmatist: Practical, solution-oriented analysis</li>
              <li>Enthusiast: Energetic and optimistic viewpoints</li>
              <li>Curator: Content organization and curation</li>
              <li>Friend: Casual, conversational tone</li>
            </ul>
          </ul>

          <h3>Web Integration</h3>
          <ul>
            <li>
              Google Search Integration: Seamlessly fetch and analyze search
              results
            </li>
            <li>Page Content Analysis: Analyze current webpage content</li>
            <li>
              Smart Context: Automatically includes relevant page information
            </li>
          </ul>

          <h3>Modern Interface</h3>
          <ul>
            <li>Clean, minimalist aesthetic</li>
            <li>Theme switching</li>
            <li>Works seamlessly in Chrome's side panel</li>
            <li>Track and revisit your conversations</li>
            <li>
              Smart chat naming for easy organization
            </li>
            <li>
              Optional panel showing processing details (URLs
              searched, tokens, speed)
            </li>
          </ul>
        </div>

        <div class="description-section">
          <h3>Description</h3>
          <p>
            Chromepanion is a privacy-first local AI web search assistant that
            transforms how you interact with information online. By combining
            the power of Google search with local AI processing through Ollama,
            it provides intelligent, contextual responses while keeping your
            data completely private.
          </p>
          <p>
            Unlike traditional AI assistants that send your data to external
            servers, Chromepanion processes everything locally on your device,
            ensuring your conversations and search queries remain private and
            secure.
          </p>
        </div>
      </div>
    </div>
  </body>
</html>

<script>
  function copyURLToClipboard() {
    const url = document.getElementById("copy-url").innerText;
    navigator.clipboard
      .writeText(url)
      .then(() => {
        const status = document.getElementById("copy-status");
        status.textContent = "Copied!";
        setTimeout(() => {
          status.textContent = "";
        }, 2000);
      })
      .catch((err) => {
        console.error("Copy failed", err);
      });
  }

  function openChromeStore() {
    const url = "https://chromewebstore.google.com/detail/chromepanion/kldldhpebabajnikgoecnifblglhoegh?authuser=2&hl=en";

    try {
      if (window.parent && window.parent !== window) {
        window.parent.postMessage({
          type: 'OPEN_EXTERNAL_URL',
          url: url
        }, '*');
        return;
      }
    } catch (e) {
      console.log("postMessage to parent failed:", e);
    }

    try {
      const newWindow = window.open(url, '_blank', 'noopener,noreferrer');
      if (newWindow) {
        return;
      }
    } catch (e) {
      console.log("window.open failed:", e);
    }

    navigator.clipboard.writeText(url).then(() => {
      alert("Due to browser security restrictions, the URL has been copied to your clipboard.\nPlease paste it in a new browser tab:\n\n" + url);
    }).catch(() => {
      alert("Please copy this URL and open it in a new tab:\n\n" + url);
    });
  }
</script>