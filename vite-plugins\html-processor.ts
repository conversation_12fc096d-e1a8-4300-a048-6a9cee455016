import { Plugin } from 'vite';
import fs from 'fs';
import path from 'path';

interface HtmlProcessorOptions {
  inputDir: string;
  outputDir?: string;
  minify?: boolean;
}

export function htmlProcessor(options: HtmlProcessorOptions): Plugin {
  const { inputDir, outputDir = 'processed', minify = true } = options;

  return {
    name: 'html-processor',
    generateBundle(opts, bundle) {
      const htmlDir = path.resolve(inputDir);
      
      if (!fs.existsSync(htmlDir)) return;

      const htmlFiles = fs.readdirSync(htmlDir)
        .filter(file => file.endsWith('.html'));

      htmlFiles.forEach(file => {
        const filePath = path.join(htmlDir, file);
        let content = fs.readFileSync(filePath, 'utf-8');

        if (minify) {
          content = content
            .replace(/\s+/g, ' ')
            .replace(/>\s+</g, '><')
            .replace(/\s+>/g, '>')
            .replace(/<\s+/g, '<')
            .trim();
        }

        const cssMatches = content.match(/<style[^>]*>([\s\S]*?)<\/style>/gi);
        if (cssMatches) {
          cssMatches.forEach((match, index) => {
            const css = match.replace(/<\/?style[^>]*>/gi, '');
            const cssFileName = `${path.basename(file, '.html')}-${index}.css`;
            
            this.emitFile({
              type: 'asset',
              fileName: `${outputDir}/${cssFileName}`,
              source: css
            });

            content = content.replace(match, 
              `<link rel="stylesheet" href="./${cssFileName}">`);
          });
        }

        this.emitFile({
          type: 'asset',
          fileName: `${outputDir}/${file}`,
          source: content
        });
      });
    }
  };
}
