import { useEffect, useRef, useCallback } from 'react';

interface ScrollRevealOptions {
  threshold?: number;
  rootMargin?: string;
  animationClass?: string;
  delay?: number;
  once?: boolean;
}

// Utility function to check if user prefers reduced motion
const prefersReducedMotion = () => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// Utility function to get animation duration in milliseconds
const getAnimationDuration = (animationClass: string): number => {
  const durations: { [key: string]: number } = {
    'animate-fade-in': 1200,
    'animate-fade-in-up': 1400,
    'animate-slide-in-left': 1300,
    'animate-slide-in-right': 1300,
    'animate-scale-in': 1100,
    'animate-slide-up': 300,
  };
  return durations[animationClass] || 1300; // Default to 1300ms
};

export const useScrollReveal = <T extends HTMLElement = HTMLDivElement>(options: ScrollRevealOptions = {}) => {
  const {
    threshold = 0.1,
    rootMargin = '50px',
    animationClass = 'animate-fade-in-up',
    delay = 0,
    once = true
  } = options;

  const elementRef = useRef<T>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hasAnimatedRef = useRef(false);
  const observerRef = useRef<IntersectionObserver | null>(null);

  const showElement = useCallback((element: HTMLElement) => {
    // Clear any pending timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // If already animated and once is true, ensure element stays visible
    if (hasAnimatedRef.current && once) {
      element.style.opacity = '';
      element.style.visibility = '';
      element.classList.add(animationClass);
      return;
    }

    // If user prefers reduced motion, show immediately without animation
    if (prefersReducedMotion()) {
      element.style.opacity = '1';
      element.style.visibility = 'visible';
      hasAnimatedRef.current = true;
      return;
    }

    timeoutRef.current = setTimeout(() => {
      if (element.isConnected) { // Check if element is still in DOM
        // Remove inline styles to let CSS animation handle the transition
        element.style.opacity = '';
        element.style.visibility = '';
        element.classList.add(animationClass);
        hasAnimatedRef.current = true;

        // Set final state after animation completes
        const animationDuration = getAnimationDuration(animationClass);
        setTimeout(() => {
          if (element.isConnected) {
            element.style.opacity = '1';
            element.style.visibility = 'visible';
          }
        }, animationDuration);
      }
      timeoutRef.current = null;
    }, delay);
  }, [animationClass, delay, once]);

  const hideElement = useCallback((element: HTMLElement) => {
    // Clear any pending timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    element.classList.remove(animationClass);
    element.style.opacity = '0';
    element.style.visibility = 'hidden';
  }, [animationClass]);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Set initial state only if not already animated
    if (!hasAnimatedRef.current) {
      element.style.opacity = '0';
      element.style.visibility = 'hidden';
    }

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const target = entry.target as HTMLElement;

          if (entry.isIntersecting) {
            showElement(target);

            if (once && hasAnimatedRef.current) {
              observer.unobserve(target);
            }
          } else if (!once && !hasAnimatedRef.current) {
            hideElement(target);
          }
        });
      },
      {
        threshold,
        rootMargin,
      }
    );

    observerRef.current = observer;
    observer.observe(element);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      observer.disconnect();
      observerRef.current = null;
    };
  }, [threshold, rootMargin, showElement, hideElement, once]);

  return elementRef;
};

// Hook for multiple elements with staggered animations
export const useStaggeredScrollReveal = <T extends HTMLElement = HTMLDivElement>(
  count: number,
  options: ScrollRevealOptions & { staggerDelay?: number } = {}
) => {
  const {
    threshold = 0.1,
    rootMargin = '50px',
    animationClass = 'animate-fade-in-up',
    delay = 0,
    staggerDelay = 100,
    once = true
  } = options;

  const containerRef = useRef<T>(null);
  const timeoutsRef = useRef<NodeJS.Timeout[]>([]);
  const hasAnimatedRef = useRef(false);
  const observerRef = useRef<IntersectionObserver | null>(null);

  const showChildren = useCallback((children: HTMLElement[]) => {
    // Clear any pending timeouts
    timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
    timeoutsRef.current = [];

    // If already animated and once is true, ensure children stay visible
    if (hasAnimatedRef.current && once) {
      children.forEach((child) => {
        child.style.opacity = '';
        child.style.visibility = '';
        child.classList.add(animationClass);
      });
      return;
    }

    // If user prefers reduced motion, show all immediately without animation
    if (prefersReducedMotion()) {
      children.forEach((child) => {
        child.style.opacity = '1';
        child.style.visibility = 'visible';
      });
      hasAnimatedRef.current = true;
      return;
    }

    const animationDuration = getAnimationDuration(animationClass);

    children.forEach((child, index) => {
      const animationTimeout = setTimeout(() => {
        if (child.isConnected) { // Check if element is still in DOM
          // Remove inline styles to let CSS animation handle the transition
          child.style.opacity = '';
          child.style.visibility = '';
          child.classList.add(animationClass);

          // Set final state after animation completes
          const finalStateTimeout = setTimeout(() => {
            if (child.isConnected) {
              child.style.opacity = '1';
              child.style.visibility = 'visible';
            }
          }, animationDuration);

          timeoutsRef.current.push(finalStateTimeout);
        }
      }, delay + (index * staggerDelay));

      timeoutsRef.current.push(animationTimeout);
    });

    hasAnimatedRef.current = true;
  }, [animationClass, delay, staggerDelay, once]);

  const hideChildren = useCallback((children: HTMLElement[]) => {
    // Clear any pending timeouts
    timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
    timeoutsRef.current = [];

    children.forEach((child) => {
      child.classList.remove(animationClass);
      child.style.opacity = '0';
      child.style.visibility = 'hidden';
    });
  }, [animationClass]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const children = Array.from(container.children) as HTMLElement[];

    // Set initial state for all children only if not already animated
    if (!hasAnimatedRef.current) {
      children.forEach((child) => {
        child.style.opacity = '0';
        child.style.visibility = 'hidden';
      });
    }

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const currentChildren = Array.from(container.children) as HTMLElement[];
            showChildren(currentChildren);

            if (once && hasAnimatedRef.current) {
              observer.unobserve(entry.target);
            }
          } else if (!once && !hasAnimatedRef.current) {
            const currentChildren = Array.from(container.children) as HTMLElement[];
            hideChildren(currentChildren);
          }
        });
      },
      {
        threshold,
        rootMargin,
      }
    );

    observerRef.current = observer;
    observer.observe(container);

    return () => {
      timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
      timeoutsRef.current = [];
      observer.disconnect();
      observerRef.current = null;
    };
  }, [count, threshold, rootMargin, showChildren, hideChildren, once]);

  return containerRef;
};
