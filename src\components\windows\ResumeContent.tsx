
import { Download } from 'lucide-react';
import { usePdfIframeResize } from '@shared/hooks/usePdfIframeResize';
import { getPrimaryEmbedUrl, getPrimaryDownloadUrl } from '@shared/config/pdfConfig';

interface ResumeContentProps {
  updateWindowSize?: (windowId: string, size: { width: number; height: number }) => void;
  windowId?: string;
}

const ResumeContent = ({ updateWindowSize, windowId }: ResumeContentProps) => {
  const { iframeRef, isLoading, loadingProgress, isVisible, handleIframeLoad } = usePdfIframeResize({
    updateWindowSize,
    windowId,
  });

  return (
    <div className="h-full flex flex-col bg-gray-100 font-tahoma text-xs relative">
      <div className="p-1 bg-gray-200 border-b border-gray-300 flex items-center space-x-1 shrink-0">
        <span className="text-gray-500 font-semibold px-2">Address</span>
        <div className="flex-1 bg-white border border-gray-400 p-0.5 flex items-center">
          <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACLSURBVDhPY2AY6OLiAjo5Of+P4CIgvwHif4z/DAyM6Asa3AyMDCysDEyWpCAg/w9iDAwMjAycgCof4x8YGBgYWBgYjAkZGBgYGFgYWEzAGBj8//9/BgaE/zMy/P9/YGAQZWBgYGD4//+fmYGBgYFB5f/l/7GNgYGBgYHh//n/dUYGBgYGDADGABo4iP2XAAAAAElFTkSuQmCC" alt="Internet Explorer browser icon - Mark Jovet Verano resume viewer" className="h-4 w-4 mr-1" />
          <input type="text" readOnly value="file:///C:/Users/<USER>/Documents/my_resume.pdf" className="w-full bg-transparent outline-none" />
        </div>
        <button className="xp-button px-2">Go</button>
      </div>
      <div className="flex-1 bg-white overflow-hidden relative">
        <iframe
          ref={iframeRef}
          src={getPrimaryEmbedUrl()}
          className="w-full h-full"
          style={{ border: 0 }}
          onLoad={handleIframeLoad}
        ></iframe>

        {isLoading && (
          <div
            className="absolute inset-0 bg-white flex items-center justify-center z-10 transition-opacity duration-500 ease-out"
            style={{
              fontFamily: 'Tahoma, sans-serif',
              opacity: isVisible ? 1 : 0
            }}
          >
            <div className="text-center bg-gray-100 border-2 border-gray-400 p-6 rounded shadow-lg" style={{
              borderTopColor: '#ffffff',
              borderLeftColor: '#ffffff',
              borderRightColor: '#808080',
              borderBottomColor: '#808080',
              minWidth: '280px'
            }}>
              <div className="text-red-600 text-5xl mb-3">📄</div>

              <p className="text-gray-700 text-sm mb-2 font-medium">Adobe Acrobat Reader</p>
              <p className="text-gray-600 text-xs mb-4">
                {loadingProgress < 40 && "Loading document..."}
                {loadingProgress >= 40 && loadingProgress < 70 && "Preparing viewer..."}
                {loadingProgress >= 70 && loadingProgress < 95 && "Optimizing display..."}
                {loadingProgress >= 95 && "Ready"}
              </p>

              <div className="bg-white border border-gray-400 h-4 w-56 mx-auto overflow-hidden" style={{
                borderTopColor: '#808080',
                borderLeftColor: '#808080',
                borderRightColor: '#ffffff',
                borderBottomColor: '#ffffff'
              }}>
                <div
                  className="h-full bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 transition-all duration-200 ease-out relative"
                  style={{ width: `${loadingProgress}%` }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
                </div>
              </div>

              <p className="text-gray-500 text-xs mt-2">{loadingProgress}%</p>
            </div>
          </div>
        )}
      </div>
      <div className="p-2 bg-gray-100 border-t border-gray-300 flex justify-end items-center shrink-0">
        <div className="flex items-center space-x-2">
          <a href={getPrimaryDownloadUrl()} download="my_resume.pdf" target="_blank" className="xp-button px-4 py-1 flex items-center space-x-2 no-underline text-black">
            <Download size={16} />
            <span>Download Resume</span>
          </a>
        </div>
      </div>
    </div>
  );
};

export default ResumeContent;
