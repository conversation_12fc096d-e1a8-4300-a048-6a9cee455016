
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Code, Database, Globe, GitBranch, Settings, Wrench } from 'lucide-react';
import SkillIcon from './SkillIcon';
import { TooltipProvider } from '@/components/ui/tooltip';
import { useScrollReveal, useStaggeredScrollReveal } from '../hooks/useScrollReveal';

interface TechnicalSkills {
  languages: string[];
  databases: string[];
  tools: string[];
  technologies: string[];
  version_control: string[];
  project_management: string[];
}

interface SkillsSectionProps {
  technicalSkills: TechnicalSkills;
}

const SkillsSection = React.forwardRef<HTMLDivElement, SkillsSectionProps>(
  ({ technicalSkills }, ref) => {
    // Scroll reveal hooks with very smooth cascade timing
    const titleRef = useScrollReveal({ animationClass: 'animate-fade-in-up', delay: 200 });
    const skillsGridRef = useStaggeredScrollReveal(6, {
      animationClass: 'animate-fade-in-up',
      delay: 600,
      staggerDelay: 300
    });

    const skillCategories = [
      {
        title: 'Languages & Frameworks',
        icon: Code,
        skills: technicalSkills.languages,
        gradient: 'from-gray-600 to-gray-800',
        darkGradient: 'dark:from-gray-200 dark:to-gray-500'
      },
      {
        title: 'Databases',
        icon: Database,
        skills: technicalSkills.databases,
        gradient: 'from-gray-500 to-gray-700',
        darkGradient: 'dark:from-gray-300 dark:to-gray-600'
      },
      {
        title: 'Development Tools',
        icon: Wrench,
        skills: technicalSkills.tools,
        gradient: 'from-gray-400 to-gray-600',
        darkGradient: 'dark:from-white dark:to-gray-400'
      },
      {
        title: 'Technologies & Libraries',
        icon: Globe,
        skills: technicalSkills.technologies,
        gradient: 'from-gray-700 to-gray-900',
        darkGradient: 'dark:from-gray-100 dark:to-gray-500'
      },
      {
        title: 'Version Control',
        icon: GitBranch,
        skills: technicalSkills.version_control,
        gradient: 'from-gray-300 to-gray-500',
        darkGradient: 'dark:from-gray-300 dark:to-gray-600'
      },
      {
        title: 'Project Management',
        icon: Settings,
        skills: technicalSkills.project_management,
        gradient: 'from-gray-800 to-black',
        darkGradient: 'dark:from-gray-200 dark:to-gray-600'
      }
    ];

    return (
      <section ref={ref} data-section="skills" className="scroll-mt-28 md:scroll-mt-32">
        <div ref={titleRef} className="text-center mb-8 md:mb-12">
          <h2 className="text-2xl md:text-3xl font-light text-gray-800 dark:text-white mb-4 tracking-tight">
            Technical Skills
          </h2>
          <div className="w-16 h-1 bg-gradient-to-r from-gray-500 to-gray-800 dark:from-white dark:to-gray-400 mx-auto rounded-full mb-4" />
          <p className="text-gray-700 dark:text-gray-300 text-lg max-w-2xl mx-auto">
            My comprehensive toolkit for building exceptional software solutions
          </p>
        </div>
        
        <TooltipProvider delayDuration={100}>
          <div ref={skillsGridRef} className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
            {skillCategories.map((category, index) => {
              const IconComponent = category.icon;
              return (
                <Card 
                  key={category.title} 
                  className="group border-0 bg-white/80 dark:bg-white/5 backdrop-blur-xl rounded-2xl shadow-lg dark:shadow-xl dark:shadow-black/10 ring-1 ring-gray-200/50 dark:ring-white/10 hover:bg-white dark:hover:bg-white/10 hover:ring-gray-300 dark:hover:ring-white/20 transition-all duration-500 hover:scale-[1.02] hover:shadow-xl dark:hover:shadow-2xl dark:hover:shadow-black/20"
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <CardHeader className="pb-4 md:pb-6">
                    <CardTitle className="flex items-center gap-3 text-lg md:text-xl font-light text-gray-900 dark:text-white group-hover:text-gray-700 dark:group-hover:text-gray-200 transition-colors">
                      <div className={`w-10 h-10 md:w-12 md:h-12 rounded-xl bg-gradient-to-r ${category.gradient} ${category.darkGradient} flex items-center justify-center shadow-lg`}>
                        <IconComponent className="w-5 h-5 md:w-6 md:h-6 text-white dark:text-black" />
                      </div>
                      {category.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex flex-wrap gap-2 md:gap-3">
                      {category.skills.map((skill) => (
                        <SkillIcon key={skill} skill={skill} />
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TooltipProvider>
      </section>
    );
  }
);

SkillsSection.displayName = 'SkillsSection';

export default SkillsSection;
