/**
 * Email rate limiting utilities for the Windows XP Portfolio
 * Implements session-based rate limiting using sessionStorage
 */

export const EMAIL_RATE_LIMIT = {
  MAX_EMAILS_PER_SESSION: 3,
  SESSION_STORAGE_KEY: 'portfolio_email_count',
  RESET_STORAGE_KEY: 'portfolio_email_reset_time',
} as const;

export interface EmailRateLimitStatus {
  count: number;
  remaining: number;
  isLimitReached: boolean;
  canSendEmail: boolean;
}

/**
 * Get current email rate limit status
 */
export function getEmailRateLimitStatus(): EmailRateLimitStatus {
  const storedCount = sessionStorage.getItem(EMAIL_RATE_LIMIT.SESSION_STORAGE_KEY);
  const count = storedCount ? parseInt(storedCount, 10) : 0;
  const remaining = Math.max(0, EMAIL_RATE_LIMIT.MAX_EMAILS_PER_SESSION - count);
  const isLimitReached = count >= EMAIL_RATE_LIMIT.MAX_EMAILS_PER_SESSION;
  
  return {
    count,
    remaining,
    isLimitReached,
    canSendEmail: !isLimitReached,
  };
}

/**
 * Increment email count and update sessionStorage
 * Returns the new count
 */
export function incrementEmailCount(): number {
  const currentStatus = getEmailRateLimitStatus();
  const newCount = currentStatus.count + 1;
  
  sessionStorage.setItem(EMAIL_RATE_LIMIT.SESSION_STORAGE_KEY, newCount.toString());
  
  // Store timestamp for potential future use (e.g., daily reset)
  const now = new Date().getTime();
  sessionStorage.setItem(EMAIL_RATE_LIMIT.RESET_STORAGE_KEY, now.toString());
  
  return newCount;
}

/**
 * Reset email count (useful for testing or manual reset)
 */
export function resetEmailCount(): void {
  sessionStorage.removeItem(EMAIL_RATE_LIMIT.SESSION_STORAGE_KEY);
  sessionStorage.removeItem(EMAIL_RATE_LIMIT.RESET_STORAGE_KEY);
}

/**
 * Get user-friendly rate limit message
 */
export function getRateLimitMessage(status: EmailRateLimitStatus): string {
  if (status.isLimitReached) {
    return `Email limit reached for this session (${status.count}/${EMAIL_RATE_LIMIT.MAX_EMAILS_PER_SESSION}). Please refresh your browser to reset.`;
  }
  
  if (status.count > 0) {
    return `Emails sent this session: ${status.count}/${EMAIL_RATE_LIMIT.MAX_EMAILS_PER_SESSION}`;
  }
  
  return `You can send up to ${EMAIL_RATE_LIMIT.MAX_EMAILS_PER_SESSION} emails per session.`;
}

/**
 * Check if user can send email and return appropriate error message if not
 */
export function validateEmailRateLimit(): { canSend: boolean; message?: string } {
  const status = getEmailRateLimitStatus();
  
  if (!status.canSendEmail) {
    return {
      canSend: false,
      message: 'Email limit reached. You can send up to 3 emails per session. Please refresh your browser to reset.',
    };
  }
  
  return { canSend: true };
}
