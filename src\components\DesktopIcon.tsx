
import { useState, useRef, useCallback } from 'react';
import { useIsMobile } from '../hooks/use-mobile';
import { useDesktopIconDrag } from '../hooks/useDesktopIconDrag';
import { IconPosition } from '../utils/iconPositionManager';

interface DesktopIconProps {
  icon: {
    id: string;
    name: string;
    icon: string;
    type: string;
  };
  position: IconPosition;
  allIconIds: string[];
  onDoubleClick: () => void;
  onPositionUpdate: (iconId: string, position: IconPosition) => void;
}

const DesktopIcon = ({
  icon,
  position,
  allIconIds,
  onDoubleClick,
  onPositionUpdate
}: DesktopIconProps) => {
  const [isSelected, setIsSelected] = useState(false);
  const [clickCount, setClickCount] = useState(0);
  const isMobile = useIsMobile();
  const clickTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const selectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Drag functionality
  const { dragState, handleMouseDown: handleDragMouseDown, handleTouchStart: handleDragTouchStart, wasClick } = useDesktopIconDrag({
    iconId: icon.id,
    currentPosition: position,
    allIconIds,
    onPositionUpdate,
    isMobile,
  });

  const handleSingleClick = useCallback(() => {
    // Just select the icon for single clicks
    setIsSelected(true);

    if (clickTimeoutRef.current) {
      clearTimeout(clickTimeoutRef.current);
    }

    // Auto-deselect after a short time
    clickTimeoutRef.current = setTimeout(() => {
      setIsSelected(false);
    }, 2000);
  }, []);

  // Separate click handling from drag handling
  const handleClick = useCallback((e: React.MouseEvent) => {
    // Only handle clicks if we're not in a drag operation
    if (!dragState.isDragging && !isMobile) {
      handleSingleClick();
    }
  }, [dragState.isDragging, isMobile, handleSingleClick]);

  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    // Prevent any drag state from interfering with double-click
    e.preventDefault();
    e.stopPropagation();

    // Clear any existing timeouts
    if (clickTimeoutRef.current) {
      clearTimeout(clickTimeoutRef.current);
    }
    if (selectionTimeoutRef.current) {
      clearTimeout(selectionTimeoutRef.current);
    }

    // Reset click count and selection state
    setClickCount(0);
    setIsSelected(false);

    // Execute double-click action
    onDoubleClick();
  }, [onDoubleClick]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    // Only start drag operation, don't handle clicks here
    handleDragMouseDown(e);
  }, [handleDragMouseDown]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (isMobile) {
      // For mobile, handle both touch interaction and potential drag
      handleDragTouchStart(e);
    }
  }, [isMobile, handleDragTouchStart]);

  const handleTouchEnd = useCallback(() => {
    // Handle touch interaction only if it was a tap, not a drag
    if (isMobile && wasClick()) {
      handleSingleClick();
    }
  }, [isMobile, handleSingleClick, wasClick]);

  // Use drag position or current position
  const displayPosition = dragState.isDragging ? dragState.currentPosition : position;

  return (
    <div
      className={`absolute flex flex-col items-center w-20 p-2 select-none ${
        dragState.isDragging ? 'z-50' : 'z-10 transition-all duration-200'
      } ${isSelected ? 'bg-blue-500 bg-opacity-30 rounded' : ''}`}
      style={{
        left: displayPosition.x,
        top: displayPosition.y,
        outline: 'none',
        border: 'none',
        cursor: dragState.isDragging ? 'grabbing' : 'grab',
        opacity: dragState.isDragging ? (dragState.isValidPosition ? 0.85 : 0.6) : 1,
        transform: dragState.isDragging ? 'scale(1.02) rotate(2deg)' : 'scale(1)',
        filter: dragState.isDragging ? 'drop-shadow(0 6px 12px rgba(0,0,0,0.25))' : 'none',
        transition: dragState.isDragging ? 'none' : 'all 0.2s ease-out',
      }}
      onMouseDown={handleMouseDown}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
    >
      <div
        className="h-10 mb-1 flex items-center justify-center transition-all duration-200"
      >
        <span
          className={`text-3xl drop-shadow-lg transition-all duration-200 ${
            dragState.isDragging && !dragState.isValidPosition ? 'text-red-300' : ''
          }`}
        >
          {icon.icon}
        </span>
      </div>
      <div
        className={`text-white text-xs text-center leading-tight font-medium drop-shadow-lg transition-all duration-200 ${
          dragState.isDragging && !dragState.isValidPosition ? 'text-red-200' : ''
        }`}
      >
        {icon.name}
      </div>

      {/* Windows XP-style drag indicator */}
      {dragState.isDragging && (
        <div
          className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white shadow-lg transition-all duration-200"
          style={{
            background: dragState.isValidPosition
              ? 'linear-gradient(45deg, #4CAF50, #45a049)'
              : 'linear-gradient(45deg, #f44336, #d32f2f)'
          }}
        />
      )}
    </div>
  );
};

export default DesktopIcon;
