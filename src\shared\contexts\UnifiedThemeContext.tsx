import React, { createContext, useContext, useState, useEffect } from 'react';

// Unified theme types
export type AppTheme = 'xp' | 'modern';
export type ModernTheme = 'light' | 'dark';

export interface UnifiedThemeContextType {
  // App theme (XP vs Modern)
  appTheme: AppTheme;
  toggleAppTheme: () => void;
  setAppTheme: (theme: AppTheme) => void;
  
  // Modern theme (Light vs Dark) - only applies when appTheme is 'modern'
  modernTheme: ModernTheme;
  toggleModernTheme: () => void;
  setModernTheme: (theme: ModernTheme) => void;
  
  // Convenience getters
  isXPTheme: boolean;
  isModernTheme: boolean;
  isModernDark: boolean;
  isModernLight: boolean;
  
  // Legacy compatibility - for gradual migration
  theme: AppTheme; // Alias for appTheme for backward compatibility
  toggleTheme: () => void; // Alias for toggleAppTheme for backward compatibility
}

const UnifiedThemeContext = createContext<UnifiedThemeContextType | undefined>(undefined);

interface UnifiedThemeProviderProps {
  children: React.ReactNode;
  defaultAppTheme?: AppTheme;
  defaultModernTheme?: ModernTheme;
}

export const UnifiedThemeProvider: React.FC<UnifiedThemeProviderProps> = ({
  children,
  defaultAppTheme = 'xp',
  defaultModernTheme = 'light'
}) => {
  const [appTheme, setAppThemeState] = useState<AppTheme>(defaultAppTheme);
  const [modernTheme, setModernThemeState] = useState<ModernTheme>(defaultModernTheme);

  // Load saved themes from localStorage on mount
  useEffect(() => {
    const savedAppTheme = localStorage.getItem('appTheme') as AppTheme;
    const savedModernTheme = localStorage.getItem('modernTheme') as ModernTheme;
    
    if (savedAppTheme && (savedAppTheme === 'xp' || savedAppTheme === 'modern')) {
      setAppThemeState(savedAppTheme);
    }
    
    if (savedModernTheme && (savedModernTheme === 'light' || savedModernTheme === 'dark')) {
      setModernThemeState(savedModernTheme);
    }
  }, []);

  // Save app theme to localStorage and handle theme switching
  useEffect(() => {
    localStorage.setItem('appTheme', appTheme);
    
    // Update body dataset for theme-specific styling
    document.body.dataset.theme = appTheme;
    
    // If switching to modern theme, also apply the modern theme class
    if (appTheme === 'modern') {
      document.documentElement.classList.toggle('dark', modernTheme === 'dark');
    } else {
      // Remove dark class when in XP theme
      document.documentElement.classList.remove('dark');
    }
  }, [appTheme, modernTheme]);

  // Save modern theme to localStorage and apply dark mode class
  useEffect(() => {
    localStorage.setItem('modernTheme', modernTheme);
    
    // Only apply dark mode class if we're in modern theme
    if (appTheme === 'modern') {
      document.documentElement.classList.toggle('dark', modernTheme === 'dark');
    }
  }, [modernTheme, appTheme]);

  // Theme setters with validation
  const setAppTheme = (theme: AppTheme) => {
    if (theme === 'xp' || theme === 'modern') {
      setAppThemeState(theme);
    }
  };

  const setModernTheme = (theme: ModernTheme) => {
    if (theme === 'light' || theme === 'dark') {
      setModernThemeState(theme);
    }
  };

  // Theme togglers
  const toggleAppTheme = () => {
    setAppTheme(appTheme === 'xp' ? 'modern' : 'xp');
  };

  const toggleModernTheme = () => {
    setModernTheme(modernTheme === 'light' ? 'dark' : 'light');
  };

  // Convenience getters
  const isXPTheme = appTheme === 'xp';
  const isModernTheme = appTheme === 'modern';
  const isModernDark = isModernTheme && modernTheme === 'dark';
  const isModernLight = isModernTheme && modernTheme === 'light';

  const contextValue: UnifiedThemeContextType = {
    // App theme
    appTheme,
    toggleAppTheme,
    setAppTheme,
    
    // Modern theme
    modernTheme,
    toggleModernTheme,
    setModernTheme,
    
    // Convenience getters
    isXPTheme,
    isModernTheme,
    isModernDark,
    isModernLight,
    
    // Legacy compatibility
    theme: appTheme,
    toggleTheme: toggleAppTheme,
  };

  return (
    <UnifiedThemeContext.Provider value={contextValue}>
      {children}
    </UnifiedThemeContext.Provider>
  );
};

// Hook to use the unified theme context
export const useUnifiedTheme = (): UnifiedThemeContextType => {
  const context = useContext(UnifiedThemeContext);
  if (context === undefined) {
    throw new Error('useUnifiedTheme must be used within a UnifiedThemeProvider');
  }
  return context;
};

// Legacy hook for backward compatibility - maps to app theme
export const useTheme = () => {
  const context = useUnifiedTheme();
  return {
    theme: context.appTheme,
    toggleTheme: context.toggleAppTheme,
  };
};

// Modern theme hook for components that only need modern theme state
export const useModernTheme = () => {
  const context = useUnifiedTheme();
  return {
    theme: context.modernTheme,
    toggleTheme: context.toggleModernTheme,
  };
};

export default UnifiedThemeContext;
