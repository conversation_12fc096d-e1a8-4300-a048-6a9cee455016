/**
 * XP Analytics Data Structure
 * Professional portfolio data optimized for Windows XP theme visualization
 */

// XP Color Palette for consistent theming
export const xpColorScheme = {
  primary: "#0078D4",      // XP Blue
  secondary: "#316AC5",    // Darker XP Blue
  accent: "#FFD700",       // XP Yellow
  success: "#107C10",      // XP Green
  warning: "#FF8C00",      // XP Orange
  background: "#F0F0F0",   // XP Gray
  border: "#808080",       // XP Border Gray
  white: "#FFFFFF",
  text: "#000000"
};

// Skills data with XP-compatible styling and proficiency ratings
export const xpSkillsData = [
  // Backend Technologies
  { name: "C#", category: "backend", proficiency: 9, years: 10, xpColor: xpColorScheme.primary, lastUsed: "2024-02" },
  { name: "VB.NET", category: "backend", proficiency: 8, years: 8, xpColor: xpColorScheme.secondary, lastUsed: "2023-12" },
  { name: "ASP.NET", category: "backend", proficiency: 9, years: 10, xpColor: xpColorScheme.primary, lastUsed: "2024-02" },
  { name: ".NET Core", category: "backend", proficiency: 8, years: 5, xpColor: xpColorScheme.primary, lastUsed: "2024-02" },
  { name: "Node.js", category: "backend", proficiency: 7, years: 3, xpColor: xpColorScheme.success, lastUsed: "2024-01" },

  // Frontend Technologies
  { name: "React", category: "frontend", proficiency: 8, years: 4, xpColor: "#61DAFB", lastUsed: "2024-02" },
  { name: "Angular", category: "frontend", proficiency: 7, years: 3, xpColor: "#DD0031", lastUsed: "2023-11" },
  { name: "TypeScript", category: "frontend", proficiency: 8, years: 4, xpColor: "#3178C6", lastUsed: "2024-02" },
  { name: "JavaScript", category: "frontend", proficiency: 9, years: 10, xpColor: "#F7DF1E", lastUsed: "2024-02" },
  { name: "jQuery", category: "frontend", proficiency: 8, years: 8, xpColor: "#0769AD", lastUsed: "2023-10" },

  // Database Technologies
  { name: "MSSQL", category: "database", proficiency: 9, years: 10, xpColor: xpColorScheme.warning, lastUsed: "2024-02" },
  { name: "MySQL", category: "database", proficiency: 8, years: 6, xpColor: "#4479A1", lastUsed: "2023-12" },
  { name: "PostgreSQL", category: "database", proficiency: 7, years: 3, xpColor: "#336791", lastUsed: "2023-09" },
  { name: "MongoDB", category: "database", proficiency: 6, years: 2, xpColor: "#47A248", lastUsed: "2023-08" },
  { name: "ElasticSearch", category: "database", proficiency: 7, years: 2, xpColor: "#005571", lastUsed: "2024-01" },

  // Development Tools
  { name: "Visual Studio", category: "tools", proficiency: 9, years: 10, xpColor: "#5C2D91", lastUsed: "2024-02" },
  { name: "VS Code", category: "tools", proficiency: 9, years: 6, xpColor: "#007ACC", lastUsed: "2024-02" },
  { name: "Docker", category: "tools", proficiency: 7, years: 3, xpColor: "#2496ED", lastUsed: "2024-01" },
  { name: "Azure DevOps", category: "tools", proficiency: 8, years: 4, xpColor: "#0078D4", lastUsed: "2024-02" },
  { name: "Postman", category: "tools", proficiency: 8, years: 5, xpColor: "#FF6C37", lastUsed: "2024-02" }
];

// Enhanced career milestones with detailed timeline data for Phase 2.1
export const xpCareerMilestones = [
  {
    id: "lbh-digital",
    date: "2023-03",
    startDate: "2023-03-01",
    endDate: "2024-02-29",
    company: "LBH Digital",
    position: "Senior Software Developer",
    location: "Australia (Remote)",
    xpIcon: "🏢",
    duration: "Mar 2023 - Feb 2024",
    durationMonths: 12,
    companyColor: "#2563EB", // Blue for enterprise
    companySize: "Medium (50-200 employees)",
    industry: "Financial Technology",
    employmentType: "Full-time Contract",
    careerLevel: "Senior",
    achievements: [
      "Maintained and enhanced web platform for loans management (Kubio Datasage)",
      "Optimized CV/excel data extraction process resulting in 40% performance improvement",
      "Implemented ElasticSearch for enhanced data storage and management",
      "Mentored junior developers on best practices and code quality"
    ],
    responsibilities: [
      "Full-stack development using C# and ASP.NET",
      "Database optimization and query performance tuning",
      "ElasticSearch implementation and data indexing",
      "Code review and technical documentation"
    ],
    technologiesUsed: ["C#", "ASP.NET", "MSSQL", "ElasticSearch", "JavaScript", "HTML5", "CSS3"],
    technologiesLearned: ["ElasticSearch", "Advanced SQL Optimization"],
    impactMetrics: [
      { metric: "Performance Improvement", value: 40, unit: "%", xpColor: xpColorScheme.success },
      { metric: "Data Processing Speed", value: 60, unit: "% faster", xpColor: xpColorScheme.success },
      { metric: "System Reliability", value: 95, unit: "% uptime", xpColor: xpColorScheme.primary },
      { metric: "Code Coverage", value: 85, unit: "%", xpColor: xpColorScheme.warning }
    ],
    keyProjects: [
      {
        name: "Kubio Datasage Platform Enhancement",
        description: "Enhanced loan management platform with improved data processing",
        technologies: ["C#", "ASP.NET", "MSSQL", "ElasticSearch"],
        impact: "40% performance improvement"
      }
    ]
  },
  {
    id: "freelance-dev",
    date: "2022-01",
    startDate: "2022-01-01",
    endDate: "2023-03-01",
    company: "Freelance Development",
    position: "Full Stack Developer",
    location: "Remote",
    xpIcon: "💻",
    duration: "Jan 2022 - Mar 2023",
    durationMonths: 14,
    companyColor: "#10B981", // Green for freelance/growth
    companySize: "Self-employed",
    industry: "Software Development Services",
    employmentType: "Freelance",
    careerLevel: "Senior",
    achievements: [
      "Developed multiple client applications using .NET and React",
      "Created browser extensions with 1000+ active users",
      "Built VS Code extensions for developer productivity",
      "Established strong client relationships with 98% satisfaction rate"
    ],
    responsibilities: [
      "Full-stack application development",
      "Browser extension development and publishing",
      "Client consultation and requirement analysis",
      "Project management and delivery"
    ],
    technologiesUsed: ["C#", ".NET Core", "React", "TypeScript", "JavaScript", "Node.js", "MongoDB"],
    technologiesLearned: ["React", "TypeScript", "Browser Extension APIs", "VS Code Extension Development"],
    impactMetrics: [
      { metric: "Client Projects", value: 15, unit: "completed", xpColor: xpColorScheme.primary },
      { metric: "Extension Users", value: 1000, unit: "+ active", xpColor: xpColorScheme.success },
      { metric: "Client Satisfaction", value: 98, unit: "%", xpColor: xpColorScheme.success },
      { metric: "Revenue Growth", value: 150, unit: "% YoY", xpColor: xpColorScheme.warning }
    ],
    keyProjects: [
      {
        name: "Browser Extensions Suite",
        description: "Productivity tools for web browsing and development",
        technologies: ["JavaScript", "TypeScript", "React"],
        impact: "1000+ active users"
      },
      {
        name: "VS Code Extensions",
        description: "Developer productivity tools for Visual Studio Code",
        technologies: ["TypeScript", "VS Code API"],
        impact: "Enhanced developer workflow"
      }
    ]
  },
  {
    id: "previous-experience",
    date: "2014-01",
    startDate: "2014-01-01",
    endDate: "2022-01-01",
    company: "Previous Experience",
    position: "Software Developer",
    location: "Philippines",
    xpIcon: "🏭",
    duration: "2014 - 2022",
    durationMonths: 96,
    companyColor: "#F59E0B", // Orange for foundational experience
    companySize: "Large (500+ employees)",
    industry: "Enterprise Software",
    employmentType: "Full-time",
    careerLevel: "Mid-level to Senior",
    achievements: [
      "Developed enterprise applications using .NET Framework",
      "Managed database systems and optimized queries",
      "Led small development teams on critical projects",
      "Progressed from Junior to Senior Developer role"
    ],
    responsibilities: [
      "Enterprise application development",
      "Database design and optimization",
      "Team leadership and mentoring",
      "System architecture and design"
    ],
    technologiesUsed: ["C#", "VB.NET", ".NET Framework", "MSSQL", "Crystal Reports", "JavaScript", "jQuery"],
    technologiesLearned: ["C#", "VB.NET", ".NET Framework", "MSSQL", "Enterprise Architecture"],
    impactMetrics: [
      { metric: "Applications Delivered", value: 25, unit: "+", xpColor: xpColorScheme.primary },
      { metric: "Database Performance", value: 50, unit: "% improved", xpColor: xpColorScheme.success },
      { metric: "Team Leadership", value: 5, unit: "developers", xpColor: xpColorScheme.warning },
      { metric: "Career Progression", value: 3, unit: "promotions", xpColor: xpColorScheme.accent }
    ],
    keyProjects: [
      {
        name: "Enterprise Resource Planning System",
        description: "Comprehensive ERP solution for business operations",
        technologies: ["C#", ".NET Framework", "MSSQL"],
        impact: "Streamlined business processes"
      },
      {
        name: "Reporting and Analytics Platform",
        description: "Business intelligence and reporting system",
        technologies: ["Crystal Reports", "MSSQL", "C#"],
        impact: "Improved decision-making capabilities"
      }
    ]
  }
];

// Technology evolution timeline showing learning progression
export const xpTechnologyEvolution = [
  { year: 2014, technologies: ["C#", "VB.NET", "MSSQL", "Visual Studio"], level: 3 },
  { year: 2016, technologies: ["ASP.NET", "jQuery", "Crystal Reports"], level: 5 },
  { year: 2018, technologies: ["JavaScript", "HTML5", "CSS3", "Bootstrap"], level: 6 },
  { year: 2020, technologies: ["React", "TypeScript", "Node.js"], level: 7 },
  { year: 2022, technologies: ["Docker", "Azure", "MongoDB"], level: 8 },
  { year: 2024, technologies: ["ElasticSearch", ".NET Core", "Microservices"], level: 9 }
];

// Project impact metrics for visualization
export const xpProjectImpacts = [
  {
    project: "Kubio Datasage Platform",
    category: "Enterprise Application",
    metrics: {
      performance: 40,
      reliability: 95,
      userSatisfaction: 92,
      codeQuality: 88
    },
    technologies: ["C#", "ASP.NET", "MSSQL", "ElasticSearch"],
    xpColor: xpColorScheme.primary
  },
  {
    project: "Browser Extensions Suite",
    category: "Productivity Tools",
    metrics: {
      performance: 85,
      reliability: 98,
      userSatisfaction: 96,
      codeQuality: 90
    },
    technologies: ["JavaScript", "TypeScript", "React"],
    xpColor: xpColorScheme.success
  },
  {
    project: "VS Code Extensions",
    category: "Developer Tools",
    metrics: {
      performance: 88,
      reliability: 97,
      userSatisfaction: 94,
      codeQuality: 92
    },
    technologies: ["TypeScript", "Node.js", "VS Code API"],
    xpColor: xpColorScheme.warning
  }
];

// Skill categories for radar chart
export const xpSkillCategories = [
  { category: "Backend Development", value: 9, xpColor: xpColorScheme.primary },
  { category: "Frontend Development", value: 8, xpColor: xpColorScheme.secondary },
  { category: "Database Management", value: 8, xpColor: xpColorScheme.warning },
  { category: "DevOps & Tools", value: 7, xpColor: xpColorScheme.success },
  { category: "Project Management", value: 8, xpColor: xpColorScheme.accent },
  { category: "Problem Solving", value: 9, xpColor: xpColorScheme.primary }
];

export default {
  xpColorScheme,
  xpSkillsData,
  xpCareerMilestones,
  xpTechnologyEvolution,
  xpProjectImpacts,
  xpSkillCategories
};
