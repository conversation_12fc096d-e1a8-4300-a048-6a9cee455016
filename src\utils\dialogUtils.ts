import { DialogType, safeOpenDialog, safeCloseDialog, isDialogCurrentlyOpen } from '../hooks/useDialogManager';

/**
 * Utility functions for managing dialogs throughout the application
 * These functions provide a safe way to interact with dialogs without causing React rendering conflicts
 */

/**
 * Opens a contact dialog with contact information
 */
export const openContactDialog = () => {
  if (isDialogCurrentlyOpen('contact')) {
    return null; // Dialog already open
  }
  
  return safeOpenDialog('contact', {
    title: 'Contact Information',
    content: 'Contact details will be displayed here'
  });
};

/**
 * Opens a notification dialog with custom message
 */
export const openNotificationDialog = (
  title: string, 
  message: string, 
  icon?: string,
  details?: string
) => {
  return safeOpenDialog('notification', {
    title,
    message,
    icon,
    details
  });
};

/**
 * Opens a confirmation dialog with custom actions
 */
export const openConfirmationDialog = (
  message: string,
  onConfirm: () => void,
  onCancel?: () => void,
  options?: {
    title?: string;
    confirmText?: string;
    cancelText?: string;
  }
) => {
  return safeOpenDialog('confirmation', {
    title: options?.title || 'Confirmation',
    message,
    onConfirm,
    onCancel,
    confirmText: options?.confirmText || 'OK',
    cancelText: options?.cancelText || 'Cancel'
  });
};

/**
 * Opens an error dialog with error information
 */
export const openErrorDialog = (
  title: string,
  message: string,
  details?: string
) => {
  return safeOpenDialog('error', {
    title,
    message,
    details
  });
};

/**
 * Opens a custom dialog with any content
 */
export const openCustomDialog = (
  type: DialogType,
  data?: any,
  options?: { allowMultiple?: boolean; id?: string }
) => {
  return safeOpenDialog(type, data, options);
};

/**
 * Closes a dialog by type or ID
 */
export const closeDialog = (identifier: string | DialogType) => {
  safeCloseDialog(identifier);
};

/**
 * Checks if a specific dialog type is currently open
 */
export const isDialogOpen = (type: DialogType): boolean => {
  return isDialogCurrentlyOpen(type);
};

/**
 * Prevents multiple instances of the same dialog from opening
 * Returns true if dialog was opened, false if already open
 */
export const openDialogSafely = (
  type: DialogType,
  data?: any,
  options?: { allowMultiple?: boolean; id?: string }
): boolean => {
  if (!options?.allowMultiple && isDialogCurrentlyOpen(type)) {
    console.log(`Dialog of type '${type}' is already open`);
    return false;
  }
  
  const result = safeOpenDialog(type, data, options);
  return result !== null;
};

/**
 * Windows XP-style message box
 */
export const showMessageBox = (
  message: string,
  title: string = 'Message',
  type: 'info' | 'warning' | 'error' = 'info'
) => {
  const icons = {
    info: 'ℹ️',
    warning: '⚠️',
    error: '❌'
  };

  return openNotificationDialog(title, message, icons[type]);
};

/**
 * Opens the search dialog with navigation callbacks
 */
export const openSearchDialog = (
  onOpenWindow?: (windowId: string, windowName: string) => void,
  onNavigate?: (item: any) => void
) => {
  if (isDialogCurrentlyOpen('search')) {
    return null; // Dialog already open
  }

  return safeOpenDialog('search', {
    onOpenWindow,
    onNavigate
  });
};

/**
 * Windows XP-style confirmation box
 */
export const showConfirmBox = (
  message: string,
  onConfirm: () => void,
  title: string = 'Confirm'
) => {
  return openConfirmationDialog(message, onConfirm, undefined, { title });
};
