<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>NPM Command Runner - VS Code Extension</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://rsms.me/inter/inter.css');
        html { font-family: 'Inter', sans-serif; }

        /* Custom gradient backgrounds for modern web 2.0 styling - Black/Grey Theme */
        .gradient-bg {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #404040 100%);
        }

        .feature-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid #d1d5db;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            border-color: #9ca3af;
        }

        .tech-badge {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-weight: 600;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            border: 1px solid #4b5563;
        }

        .tech-badge:hover {
            background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
            transform: translateY(-1px);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200 text-gray-800 min-h-screen">
    <div class="container mx-auto max-w-6xl py-8 px-4">

        <!-- Header Section -->
        <header class="text-center mb-16">
            <div class="gradient-bg text-white p-8 rounded-2xl shadow-2xl mb-8">
                <div class="flex items-center justify-center mb-4">
                    <div class="bg-white/20 p-4 rounded-xl mr-4">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                        </svg>
                    </div>
                    <h1 class="text-5xl font-bold">NPM Command Runner</h1>
                </div>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                    A VS Code extension to intelligently manage and run custom npm commands with advanced package.json discovery capabilities.
                </p>
            </div>
        </header>

        <!-- Demo GIF Section -->
        <div class="mb-16">
            <div class="bg-white p-4 rounded-xl shadow-lg border border-gray-300">
                <div class="flex justify-center">
                    <img src="/gifs/commandrunner.gif" alt="NPM Command Runner Demo" class="rounded-lg shadow-xl w-full h-auto border border-gray-300" style="max-height: 700px;">
                </div>
            </div>
        </div>

        <!-- CTA Section -->
        <div class="text-center mb-16">
            <div class="bg-white p-8 rounded-xl shadow-lg border border-gray-300">
                <h2 class="text-2xl font-bold mb-4 text-gray-900">Get Started Now</h2>
                <div class="flex flex-col md:flex-row items-center justify-center gap-4 mb-6">
                    <button onclick="openMarketplace()" class="bg-gradient-to-r from-gray-800 to-gray-900 text-white font-bold py-4 px-8 rounded-lg shadow-lg hover:from-gray-700 hover:to-gray-800 transition-all transform hover:scale-105">
                        View on VS Code Marketplace
                    </button>
                    <div class="text-gray-600">
                        <strong>Quick Install:</strong> Search "npm command runner" in VS Code
                    </div>
                </div>
            </div>
        </div>

        <!-- Description Section -->
        <div class="bg-white p-8 rounded-xl shadow-lg border border-gray-300 mb-12">
            <h3 class="text-3xl font-bold mb-6 text-gray-900">About This Extension</h3>
            <div class="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                <p class="text-lg mb-4">
                    A VS Code extension to intelligently manage and run custom npm commands with advanced package.json discovery capabilities.
                </p>
            </div>
        </div>

        <!-- Features Grid -->
        <div class="grid md:grid-cols-2 gap-8 mb-12">
            <!-- Intelligent Package.json Discovery -->
            <div class="feature-card p-8 rounded-xl">
                <div class="flex items-center mb-4">
                    <div class="bg-gray-100 p-3 rounded-lg mr-4 border border-gray-300">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-gray-700">
                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                            <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
                            <line x1="12" y1="22.08" x2="12" y2="12"/>
                        </svg>
                    </div>
                    <h4 class="text-xl font-bold text-gray-900">Intelligent Package.json Discovery</h4>
                </div>
                <ul class="space-y-2 text-gray-700">
                    <li>• <strong>Recursive Search:</strong> Automatically finds package.json files throughout your workspace</li>
                    <li>• <strong>Multi-level Traversal:</strong> Searches both up and down the directory tree from any starting point</li>
                    <li>• <strong>Smart Detection:</strong> Intelligently identifies the most relevant package.json for your current context</li>
                    <li>• <strong>Workspace Awareness:</strong> Understands VS Code workspace boundaries and common project structures</li>
                </ul>
            </div>

            <!-- Project Context Awareness -->
            <div class="feature-card p-8 rounded-xl">
                <div class="flex items-center mb-4">
                    <div class="bg-gray-200 p-3 rounded-lg mr-4 border border-gray-400">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-gray-800">
                            <path d="M9 11H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h4"/>
                            <path d="M15 11h4a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-4"/>
                            <path d="M12 2v20"/>
                        </svg>
                    </div>
                    <h4 class="text-xl font-bold text-gray-900">Project Context Awareness</h4>
                </div>
                <ul class="space-y-2 text-gray-700">
                    <li>• <strong>Monorepo Support:</strong> Detects and handles monorepo structures (Lerna, Nx, Rush, Yarn/npm workspaces)</li>
                    <li>• <strong>Framework Detection:</strong> Recognizes React, Vue, Angular, Next.js, Nuxt, Express, NestJS, and more</li>
                    <li>• <strong>Contextual Suggestions:</strong> Provides intelligent command suggestions based on detected project type</li>
                    <li>• <strong>Automatic Selection:</strong> Auto-selects the most appropriate package.json when possible</li>
                </ul>
            </div>

            <!-- Memory & Persistence -->
            <div class="feature-card p-8 rounded-xl">
                <div class="flex items-center mb-4">
                    <div class="bg-gray-300 p-3 rounded-lg mr-4 border border-gray-500">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-gray-900">
                            <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
                        </svg>
                    </div>
                    <h4 class="text-xl font-bold text-gray-900">Memory & Persistence</h4>
                </div>
                <ul class="space-y-2 text-gray-700">
                    <li>• <strong>Remember Selections:</strong> Saves your package.json preferences across sessions</li>
                    <li>• <strong>Usage History:</strong> Tracks recently used package.json files</li>
                    <li>• <strong>Workspace Change Detection:</strong> Automatically adapts when workspace folders change</li>
                    <li>• <strong>Smart Caching:</strong> Caches discovery results for improved performance</li>
                </ul>
            </div>

            <!-- Enhanced User Interface -->
            <div class="feature-card p-8 rounded-xl">
                <div class="flex items-center mb-4">
                    <div class="bg-gray-400 p-3 rounded-lg mr-4 border border-gray-600">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-white">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                            <circle cx="9" cy="9" r="2"/>
                            <path d="M21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                        </svg>
                    </div>
                    <h4 class="text-xl font-bold text-gray-900">Enhanced User Interface</h4>
                </div>
                <ul class="space-y-2 text-gray-700">
                    <li>• <strong>Package.json Selector:</strong> Easy-to-use interface for selecting between multiple package.json files</li>
                    <li>• <strong>Context Information:</strong> Shows project type, location, and relevance scores</li>
                    <li>• <strong>Status Bar Integration:</strong> Shows current package.json in the status bar</li>
                    <li>• <strong>Visual Indicators:</strong> Clear icons and descriptions for different project types</li>
                </ul>
            </div>
        </div>

        <!-- Technology Stack -->
        <div class="bg-white p-8 rounded-xl shadow-lg border border-gray-300 mb-12">
            <h3 class="text-2xl font-bold mb-6 text-gray-900">Technology Stack</h3>
            <div class="flex flex-wrap gap-3">
                <span class="tech-badge">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M0 0h24v24H0V0z" fill="none"/>
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/>
                    </svg>
                    TypeScript
                </span>
                <span class="tech-badge">VS Code Extension API</span>
                <span class="tech-badge">Node.js</span>
                <span class="tech-badge">JSON Parsing</span>
                <span class="tech-badge">File System API</span>
                <span class="tech-badge">Workspace API</span>
            </div>
        </div>

        <!-- Installation Guide -->
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-8 rounded-xl border border-gray-300 mb-12">
            <h3 class="text-2xl font-bold mb-6 text-gray-900">Installation Instructions</h3>
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-bold text-lg mb-3 text-gray-800">Method 1: VS Code IDE</h4>
                    <ol class="list-decimal list-inside space-y-2 text-gray-700">
                        <li>Open VS Code</li>
                        <li>Go to Extensions (Ctrl+Shift+X)</li>
                        <li>Search for "npm command runner"</li>
                        <li>Click Install</li>
                    </ol>
                </div>
                <div>
                    <h4 class="font-bold text-lg mb-3 text-gray-800">Method 2: VS Code Marketplace</h4>
                    <ol class="list-decimal list-inside space-y-2 text-gray-700">
                        <li>Visit the VS Code Marketplace</li>
                        <li>Search for "npm command runner"</li>
                        <li>Click Install</li>
                        <li>Open in VS Code when prompted</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Alternative Access -->
        <div class="text-center mt-12 p-8 bg-gray-100 rounded-xl border border-gray-400">
            <p class="font-semibold mb-4 text-gray-900">Alternative ways to access:</p>
            <div class="flex flex-col md:flex-row items-center justify-center gap-4">
                <a href="https://marketplace.visualstudio.com/items?itemName=markoverano.npm-command-runner" onclick="openMarketplace(); return false;" class="text-gray-800 hover:text-black hover:underline font-medium">
                    Click to open VS Code Marketplace
                </a>
                <div class="flex items-center gap-2">
                    <code id="copy-url" class="bg-white p-3 border border-gray-400 rounded-md text-sm text-gray-600 cursor-pointer hover:bg-gray-50 transition-colors" onclick="copyURLToClipboard()">
                        https://marketplace.visualstudio.com/items?itemName=markoverano.npm-command-runner
                    </code>
                    <span id="copy-status" class="text-gray-800 text-sm font-medium"></span>
                </div>
            </div>
        </div>

    </div>

    <script>
      function copyURLToClipboard() {
        const url = document.getElementById("copy-url").innerText;
        navigator.clipboard
          .writeText(url)
          .then(() => {
            const status = document.getElementById("copy-status");
            status.textContent = "Copied!";
            setTimeout(() => {
              status.textContent = "";
            }, 2000);
          })
          .catch((err) => {
            console.error("Copy failed", err);
          });
      }

      function openMarketplace() {
        const url = "https://marketplace.visualstudio.com/items?itemName=markoverano.npm-command-runner";

        try {
          if (window.parent && window.parent !== window) {
            window.parent.postMessage({
              type: 'OPEN_EXTERNAL_URL',
              url: url
            }, '*');
            return;
          }
        } catch (e) {
          console.log("postMessage to parent failed:", e);
        }

        try {
          const newWindow = window.open(url, '_blank', 'noopener,noreferrer');
          if (newWindow) {
            return;
          }
        } catch (e) {
          console.log("window.open failed:", e);
        }

        navigator.clipboard.writeText(url).then(() => {
          alert("Due to browser security restrictions, the URL has been copied to your clipboard.\nPlease paste it in a new browser tab:\n\n" + url);
        }).catch(() => {
          alert("Please copy this URL and open it in a new tab:\n\n" + url);
        });
      }
    </script>
</body>
</html>
