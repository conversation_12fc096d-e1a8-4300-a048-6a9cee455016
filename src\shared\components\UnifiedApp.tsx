import React from 'react';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@shared/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useUnifiedTheme } from "@shared/contexts/UnifiedThemeContext";
import { cn } from "@shared/utils";

// Import theme-specific components
import XPIndex from "../../pages/Index";
import XPNotFound from "../../pages/NotFound";
import ModernIndex from "../../modern/src/pages/Index";
import SEOHead from "../../components/SEOHead";

// Create a single query client instance
const queryClient = new QueryClient();

/**
 * Unified App component that handles theme-aware rendering
 * Consolidates the logic from both App.tsx files
 */
const UnifiedApp: React.FC = () => {
  const { isXPTheme, isModernTheme } = useUnifiedTheme();

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <SEOHead />
        <Toaster />
        <Sonner />
        <div className={cn(
          isXPTheme ? 'bg-background text-foreground' : '',
          isModernTheme ? 'min-h-screen relative overflow-hidden bg-background transition-colors duration-300' : '',
          "min-h-screen"
        )}>
          <BrowserRouter>
            <Routes>
              {isXPTheme ? (
                <>
                  <Route path="/" element={<XPIndex />} />
                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<XPNotFound />} />
                </>
              ) : (
                <>
                  <Route path="/" element={<ModernIndex />} />
                  <Route path="*" element={<ModernIndex />} />
                </>
              )}
            </Routes>
          </BrowserRouter>
        </div>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default UnifiedApp;
