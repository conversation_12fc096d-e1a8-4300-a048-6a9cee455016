export interface BrowserExtension {
  id: string;
  name: string;
  description: string;
  tagline: string;
  icon: string;
  technologies: string[];
  status: {
    published: boolean;
    version: string;
    platform: string;
    offlineSupport: boolean;
  };
  links: {
    chromeWebStore: string;
    searchTerm: string;
  };
  features: {
    category: string;
    items: string[];
  }[];
  screenshots: string[];
  projectId: string;
  windowName: string;
}

export const browserExtensions: BrowserExtension[] = [
  {
    id: 'zendo',
    name: 'Zendo',
    description: 'A minimalist to-do list with task grouping, drag-and-drop, and color priorities.',
    tagline: 'Grouped To-Do List & Task Manager for Chrome',
    icon: '🧘',
    technologies: ['JavaScript', 'Chrome Extension API', 'HTML5', 'CSS3', 'Chrome Sync'],
    status: {
      published: true,
      version: 'Latest',
      platform: 'Chrome Web Store',
      offlineSupport: true
    },
    links: {
      chromeWebStore: 'https://chromewebstore.google.com/detail/zendo/docecfpjajpjlfpidlbmdodgaopdgdfo',
      searchTerm: 'Zendo Browser Extension'
    },
    features: [
      {
        category: 'Task Management',
        items: [
          'Organize tasks into groups',
          'Set color-coded priorities',
          'Drag-and-drop interface',
          'Clean, minimalist design'
        ]
      },
      {
        category: 'Ideal For',
        items: [
          'Daily task planning',
          'Project-based to-do management',
          'Students, creatives, and professionals seeking simplicity',
          'Anyone who prefers grouped over nested tasks'
        ]
      },
      {
        category: 'Technical Features',
        items: [
          'Works fully offline',
          'Chrome Sync integration',
          'Cross-device synchronization',
          'Local data storage'
        ]
      }
    ],
    screenshots: ['/projects/z-task.png'],
    projectId: 'zendo',
    windowName: 'Zendo - Browser Extension'
  },
  {
    id: 'chromepanion',
    name: 'Chromepanion',
    description: 'Privacy-first local AI web search assistant that transforms how you interact with information online.',
    tagline: 'Your ultimate companion for browsing with a layer of privacy..',
    icon: '🤖',
    technologies: ['JavaScript', 'Chrome Extension API', 'HTML5', 'CSS3', 'React'],
    status: {
      published: true,
      version: 'Latest',
      platform: 'Chrome Web Store',
      offlineSupport: true
    },
    links: {
      chromeWebStore: 'https://chromewebstore.google.com/detail/chromepanion/kldldhpebabajnikgoecnifblglhoegh?authuser=2&hl=en',
      searchTerm: 'Chromepanion Browser Extension'
    },
    features: [
      {
        category: 'Privacy-First Design',
        items: [
          '100% Local Processing: All AI processing happens locally via Ollama',
          'No External AI Services: No data sent to OpenAI, Claude, or other cloud providers',
          'Private Search: Your search queries and results stay on your device'
        ]
      },
      {
        category: 'AI-Powered Analysis',
        items: [
          '10 Specialized Personas: Each with unique analysis styles and expertise',
          'Scholar: Academic, research-focused analysis',
          'Executive: Business and strategic insights',
          'Storyteller: Narrative and creative perspectives',
          'Skeptic: Critical thinking and fact-checking',
          'Mentor: Guidance and educational approach',
          'Investigator: Deep research and fact-finding',
          'Pragmatist: Practical, solution-oriented analysis',
          'Enthusiast: Energetic and optimistic viewpoints',
          'Curator: Content organization and curation',
          'Friend: Casual, conversational tone'
        ]
      },
      {
        category: 'Web Integration',
        items: [
          'Google Search Integration: Seamlessly fetch and analyze search results',
          'Page Content Analysis: Analyze current webpage content',
          'Smart Context: Automatically includes relevant page information'
        ]
      },
      {
        category: 'Modern Interface',
        items: [
          'Clean, minimalist aesthetic',
          'Theme switching',
          'Works seamlessly in Chrome\'s side panel',
          'Track and revisit your conversations',
          'Smart chat naming for easy organization',
          'Optional panel showing processing details (URLs searched, tokens, speed)'
        ]
      }
    ],
    screenshots: ['/chromepanion.png', '/chromepanion-b.png'],
    projectId: 'chromepanion',
    windowName: 'Chromepanion - Browser Extension'
  }
];

// Helper function to get browser extensions for modern theme
export const getBrowserExtensionsForModernTheme = () => {
  return browserExtensions.map(ext => ({
    name: ext.name,
    description: ext.description,
    technologies: ext.technologies,
    link: ext.links.chromeWebStore,
    status: ext.status.published ? 'Published' : 'In Development'
  }));
};

// Helper function to get project folder data for retro theme
export const getBrowserExtensionFolders = () => {
  return browserExtensions.map(ext => ({
    name: ext.name,
    description: 'Browser Extension',
    icon: ext.icon,
    projectId: ext.projectId,
    windowName: ext.windowName
  }));
};
