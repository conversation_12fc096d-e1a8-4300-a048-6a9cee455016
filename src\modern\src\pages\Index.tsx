import React, { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Chrome, Smartphone, Terminal } from "lucide-react";
import ApiMonitor from "../components/ApiMonitor";
import ProfileSection from "../components/ProfileSection";
import ExperienceSection from "../components/ExperienceSection";
import SkillsSection from "../components/SkillsSection";
import ProjectsSection from "../components/ProjectsSection";
import Navigation from "../components/Navigation";
import ThemeSwitcher from "../components/ThemeSwitcher";
import { getModernThemeSkillsData } from "@shared/data/skillsData";
import { getBrowserExtensionsForModernTheme } from "@shared/lib/browserExtensions";

const Index = () => {
  const [showMobileNav, setShowMobileNav] = useState(false);
  const [activeContact, setActiveContact] = useState<string | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const profileRef = useRef<HTMLDivElement>(null);
  const projectsRef = useRef<HTMLDivElement>(null);
  const skillsRef = useRef<HTMLDivElement>(null);
  const experienceRef = useRef<HTMLDivElement>(null);

  // Mouse tracking for interactive background
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, []);

  // Removed old conflicting scroll reveal system - now handled by individual component hooks

  const simulateApiCall = async (endpoint: string) => {
    try {
      await fetch(endpoint);
    } catch (error) {
      console.log(`Simulated API call for ${endpoint}`);
    }
  };

  const scrollToSection = (sectionName: string) => {
    const refs = {
      profile: profileRef,
      projects: projectsRef,
      skills: skillsRef,
      experience: experienceRef,
    };

    const ref = refs[sectionName as keyof typeof refs];
    ref?.current?.scrollIntoView({ behavior: "smooth" });
    setShowMobileNav(false);
  };

  const profileData = {
    name: "Mark Jovet Verano",
    title: "Senior Full-Stack Software Engineer",
    bio: "a passionate software engineer with over a decade of experience building applications and scalable systems.",
    email: "<EMAIL>",
    phone: "(+63) ************",
    location: "Philippines",
    github: "https://github.com/markoverano",
    linkedin: "https://linkedin.com/in/markoverano",
    // cvUrl removed - now using shared PDF configuration
  };

  const handleContactClick = (contactType: string) => {
    setActiveContact(activeContact === contactType ? null : contactType);
  };

  const workExperiences = [
    {
      id: 1,
      position: "Senior Software Developer",
      company: "LBH Digital (Australia)",
      duration: "Mar. 2023 - Feb. 2024",
      achievements: [
        "Involved in maintenance and creating new features for the company's web platform for loans management (Kubio Datasage)",
        "Optimized and accelerated the company's CV/excel data extraction process through refactoring and streamlining the extraction methods resulting in significant time and resource savings",
        "Employed ElasticSearch for storage of parsed files and data management",
      ],
    },
    {
      id: 2,
      position: "Senior Software Developer",
      company: "Clericalsoft Solutions (Davao City)",
      duration: "Apr. 2023 - Aug. 2023",
      achievements: [
        "Keyword Chef (US) - collaborated on SEO software projects including Keyword Finder web app used by SEOs and bloggers",
        "Niche Finder - developed tools used by bloggers, SEOs and PPC advertisers",
        "Velotio Technologies (India) - collaborated on Jobility, a job/gig matching platform built in ReactJs and .Net",
      ],
    },
    {
      id: 3,
      position: "Senior Software Developer",
      company: "Infivex | Teqto (Belgium)",
      duration: "Oct. 2022 - June. 2023",
      achievements: [
        "Involved in design, maintenance and creating new features for company's software clients with integration to Exact Online ERP Suite",
        "Trafiroad - developed web-app for tracking sales processes and assembly-line activity flow for road signages and equipments",
        "Beyers Plastics - created web-app for tracking sales processes and cleanroom processes for medical-grade plastics used by pharmaceutical companies in the Netherlands",
      ],
    },
    {
      id: 4,
      position: "Senior Software Developer",
      company: "Cashjar | Payment HQ (US)",
      duration: "Oct. 2019 - Oct. 2022",
      achievements: [
        "Involved in maintenance and adding new features for the company's financial software and in-house apps",
        "Handled daily monitoring of system activities using Azure Application Insights, Log Analytics and troubleshooting",
        "Provided night-shift support for system's, technical teams' and US-based users' post-deployment reports and feedback",
        "Managed 4 software projects: Merchant Admin, Merchant Portal, Cashjar Port, and Cashjar Mobile (Nativescript)",
        "Developed DB Migration Tool for rapid update/syncing of databases across environments",
      ],
    },
    {
      id: 5,
      position: "Software Developer",
      company: "Itmaskinen Consulting AB. (Sweden)",
      duration: "Apr. 2019 - Oct. 2019",
      achievements: [
        "Involved in designing software for the company's clients",
        "TimeTracker (in-house) - developed tool used by company employees for time and record-keeping of tasks and projects",
        "NBS (client) - created CRM software for organizing Events",
        "BIM Ctrl (client) - built Property Assessment / Security Management software",
      ],
    },
    {
      id: 6,
      position: "Software Developer",
      company: "DATASOFTLOGIC CORP., Davao City",
      duration: "Oct. 2014 - Nov 2018",
      achievements: [
        "Part of development team for the company's Health Care System (Hospice WebApp) used by Hospice agencies in the United States",
        "Focused on development of new features, bug fixing and system improvements",
        "Worked with healthcare-specific requirements and compliance standards",
      ],
    },
    {
      id: 7,
      position: "Software Developer",
      company: "CARVE BUSINESS MANAGEMENT AND SERVICES, Davao City",
      duration: "Feb 2014 - May 2014",
      achievements: [
        "Re-developed, enhanced, and translated legacy code (to .NET) of the Inventory System previously built on PHP, MySQL",
        "Started initial development of the company's in-house Human Resource and Payroll System",
      ],
    },
    {
      id: 8,
      position: "Team Lead | Software Developer",
      company: "i.ekZeed SOFTWARE DESIGN AND DEVELOPMENT CO., Davao City",
      duration: "June 2013 - Oct 2014",
      achievements: [
        "Led a team of 4 junior developers in developing the company's customizable ERP system",
        "Maintained and added new features specific to client requests tailored to their business needs",
        "Developed web app version of the desktop software",
        "Managed software solutions for clients including MyGas Petroleum Corp, Goodyear/Mike Servitek, NexGear Autoshop, A1 Tires And Battery Supplies, DCounter Hotel, and Rhodwill Construction",
      ],
    },
  ];

  const technicalSkills = getModernThemeSkillsData();
  const browserExtensionsData = getBrowserExtensionsForModernTheme();

  const projects = [
    {
      id: 1,
      title: "Browser Extensions",
      description:
        "Collection of powerful browser extensions that enhance productivity and user experience",
      icon: Chrome,
      items: browserExtensionsData.map(ext => ({
        text: `${ext.name} - ${ext.description}`,
        url: ext.link,
        isClickable: true
      })),
      color: "blue",
    },
    {
      id: 2,
      title: "VS Code Extensions",
      description:
        "VS Code extensions that enhance development workflow and productivity",
      icon: Terminal,
      items: [
        {
          text: "NPM Command Runner - Intelligently manage and run custom npm commands with advanced package.json discovery",
          url: "https://marketplace.visualstudio.com/items?itemName=markoverano.npm-command-runner",
          isClickable: true
        },
      ],
      color: "purple",
    },
    // {
    //   id: 3,
    //   title: "Mobile Apps",
    //   description:
    //     "Cross-platform mobile applications built with modern technologies",
    //   icon: Smartphone,
    //   items: [
    //     { text: "Task Management App", isClickable: false },
    //     { text: "Weather Dashboard", isClickable: false },
    //     { text: "Fitness Tracker", isClickable: false },
    //     { text: "Recipe Finder", isClickable: false },
    //     { text: "Budget Planner", isClickable: false },
    //   ],
    //   color: "green",
    // },
  ];

  return (
    <div className="min-h-screen relative overflow-hidden bg-background transition-colors duration-300">
      {/* Theme-aware Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-gray-100 via-gray-50 to-gray-200 dark:from-slate-950 dark:via-gray-900 dark:to-black transition-all duration-300">
        {/* Grid Pattern */}
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(rgba(107, 114, 128, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(107, 114, 128, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: "50px 50px",
          }}
        />

        {/* Floating Orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gray-500/10 dark:bg-white/5 rounded-full blur-3xl animate-pulse" />
        <div className="absolute top-3/4 right-1/4 w-80 h-80 bg-gray-600/10 dark:bg-gray-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute bottom-1/4 left-1/3 w-64 h-64 bg-gray-400/10 dark:bg-gray-400/5 rounded-full blur-3xl animate-pulse delay-2000" />

        {/* Interactive Mouse Glow */}
        <div
          className="absolute w-96 h-96 bg-gradient-radial from-gray-400/5 dark:from-white/5 to-transparent rounded-full blur-xl pointer-events-none transition-all duration-300"
          style={{
            left: mousePosition.x - 192,
            top: mousePosition.y - 192,
          }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10">
        <ThemeSwitcher />
        <Navigation
          showMobileNav={showMobileNav}
          onToggleMobileNav={() => setShowMobileNav(!showMobileNav)}
          onScrollToSection={scrollToSection}
        />

        {/* Hero Section */}
        <div className="container mx-auto px-4 md:px-6 pt-20 md:pt-24 pb-6 max-w-6xl">
          <div className="max-w-4xl mx-auto space-y-16">
            <ProfileSection
              ref={profileRef}
              profileData={profileData}
              activeContact={activeContact}
              onContactClick={handleContactClick}
            />

            <ProjectsSection ref={projectsRef} projects={projects} />

            <SkillsSection ref={skillsRef} technicalSkills={technicalSkills} />

            <ExperienceSection
              ref={experienceRef}
              workExperiences={workExperiences}
            />
          </div>

          {/* API Monitor */}
          {/* <ApiMonitor /> */}
        </div>
      </div>
    </div>
  );
};

export default Index;
