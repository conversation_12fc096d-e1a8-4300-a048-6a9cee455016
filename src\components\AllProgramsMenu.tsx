
import React from 'react';
import { Globe, Smartphone, Paintbrush } from 'lucide-react';
import { useUnifiedTheme } from '@/shared/contexts/UnifiedThemeContext';

const ProgramMenuItem = ({ icon, name, onClick }: { icon: React.ReactNode, name: string, onClick?: () => void }) => {
    return (
        <button
            onClick={onClick}
            className="w-full flex items-center justify-between space-x-3 px-2 py-1.5 hover:bg-[#316AC5] hover:text-white text-left text-sm rounded-sm transition-colors duration-150 group"
        >
            <div className="flex items-center space-x-2">
                <div className="w-6 h-6 flex items-center justify-center">{icon}</div>
                <span className="text-black group-hover:text-white">{name}</span>
            </div>
        </button>
    );
};

const AllProgramsMenu = () => {
    const { toggleAppTheme } = useUnifiedTheme();

    const openLink = (url: string) => {
        window.open(url, '_blank', 'noopener,noreferrer');
    };

    const openProjectDemo = (projectPath: string) => {
        const baseUrl = window.location.origin;
        const fullUrl = `${baseUrl}/projects/${projectPath}`;
        window.open(fullUrl, '_blank', 'noopener,noreferrer');
    };

    return (
        <div
            className="absolute bottom-0 left-full w-[240px] bg-white border-2 border-t-[#769bce] border-l-[#769bce] border-r-[#183969] border-b-[#183969] shadow-2xl rounded-tr-lg rounded-br-lg animate-in fade-in duration-150"
            style={{ fontFamily: 'Tahoma, sans-serif' }}
            onClick={(e) => e.stopPropagation()}
        >
             <div className="p-1 space-y-0.5">
                <ProgramMenuItem
                    icon={<span className="text-lg">🧘</span>}
                    name="Zendo"
                    onClick={() => openLink('https://chromewebstore.google.com/detail/zendo/docecfpjajpjlfpidlbmdodgaopdgdfo')}
                />
                <ProgramMenuItem
                    icon={<span className="text-lg">🤖</span>}
                    name="Chromepanion"
                    onClick={() => openLink('https://chromewebstore.google.com/detail/chromepanion/kldldhpebabajnikgoecnifblglhoegh?authuser=2&hl=en')}
                />
                <ProgramMenuItem
                    icon={<span className="text-lg">⚡</span>}
                    name="NPM Command Runner"
                    onClick={() => openLink('https://marketplace.visualstudio.com/items?itemName=markoverano.npm-command-runner')}
                />
                <ProgramMenuItem
                    icon={<Smartphone size={20} className="text-blue-700"/>}
                    name="Mobile App"
                    onClick={() => openProjectDemo('moverzz.html')}
                />
                <ProgramMenuItem
                    icon={<Paintbrush size={20} className="text-purple-700"/>}
                    name="Theme Switcher"
                    onClick={toggleAppTheme}
                />
             </div>
        </div>
    );
};

export default AllProgramsMenu;
