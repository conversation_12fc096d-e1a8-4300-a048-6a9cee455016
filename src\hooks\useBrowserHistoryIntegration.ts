import { useEffect, useCallback, useRef } from 'react';
import { useIsMobile } from './use-mobile';
import { NavigationState, MobileNavigationManager } from './useMobileNavigation';

interface BrowserHistoryIntegrationOptions {
  navigationManager: MobileNavigationManager;
  onNavigateBack?: (previousState: NavigationState | null) => void;
  onNavigateToHome?: () => void;
  onAppExit?: () => void;
}

/**
 * Hook that integrates mobile navigation with browser history API
 * Handles popstate events and manages URL state for mobile back button functionality
 */
export const useBrowserHistoryIntegration = ({
  navigationManager,
  onNavigateBack,
  onNavigateToHome,
  onAppExit
}: BrowserHistoryIntegrationOptions) => {
  const isMobile = useIsMobile();
  const isHandlingPopStateRef = useRef(false);
  const hasInitializedHistoryRef = useRef(false);

  // Initialize browser history with current state
  useEffect(() => {
    if (!isMobile || hasInitializedHistoryRef.current) return;

    hasInitializedHistoryRef.current = true;
    
    // Replace current history entry with our navigation state
    const currentState = navigationManager.currentState;
    if (currentState) {
      window.history.replaceState(
        { navigationState: currentState },
        '',
        window.location.pathname + window.location.search
      );
    }
  }, [isMobile, navigationManager.currentState]);

  // Push new history entries when navigation state changes
  useEffect(() => {
    if (!isMobile || !navigationManager.currentState || isHandlingPopStateRef.current) return;

    const currentState = navigationManager.currentState;
    
    // Only push new history entry if we're not at the initial desktop state
    if (currentState.type !== 'desktop' || navigationManager.navigationHistory.length > 1) {
      window.history.pushState(
        { navigationState: currentState },
        '',
        window.location.pathname + window.location.search
      );
    }
  }, [isMobile, navigationManager.currentState, navigationManager.navigationHistory.length]);

  // Handle browser back button
  const handlePopState = useCallback((event: PopStateEvent) => {
    if (!isMobile) return;

    isHandlingPopStateRef.current = true;

    try {
      const historyState = event.state?.navigationState as NavigationState | undefined;
      
      // If we're at the home state and user presses back, allow app to close
      if (navigationManager.isAtHome) {
        onAppExit?.();
        return;
      }

      // Attempt to go back in our navigation history
      const didGoBack = navigationManager.goBack();
      
      if (didGoBack) {
        const previousState = navigationManager.currentState;
        
        // Call appropriate navigation handler
        if (previousState?.type === 'desktop') {
          onNavigateToHome?.();
        } else {
          onNavigateBack?.(previousState);
        }
      } else {
        // If we can't go back in our history, allow app to close
        onAppExit?.();
      }
    } finally {
      // Reset the flag after a short delay to allow state updates to complete
      setTimeout(() => {
        isHandlingPopStateRef.current = false;
      }, 100);
    }
  }, [
    isMobile,
    navigationManager,
    onNavigateBack,
    onNavigateToHome,
    onAppExit
  ]);

  // Add popstate event listener
  useEffect(() => {
    if (!isMobile) return;

    window.addEventListener('popstate', handlePopState);
    
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [isMobile, handlePopState]);

  // Utility function to manually trigger back navigation
  const triggerBackNavigation = useCallback(() => {
    if (!isMobile) return false;

    if (navigationManager.isAtHome) {
      onAppExit?.();
      return false;
    }

    const didGoBack = navigationManager.goBack();
    if (didGoBack) {
      const previousState = navigationManager.currentState;
      
      if (previousState?.type === 'desktop') {
        onNavigateToHome?.();
      } else {
        onNavigateBack?.(previousState);
      }
    }

    return didGoBack;
  }, [isMobile, navigationManager, onNavigateBack, onNavigateToHome, onAppExit]);

  return {
    triggerBackNavigation,
    isAtHome: navigationManager.isAtHome,
    canGoBack: navigationManager.canGoBack
  };
};
