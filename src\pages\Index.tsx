
import { useState, useEffect } from 'react';
import BootScreen from '../components/BootScreen';
import Desktop from '../components/Desktop';
import { DialogProvider } from '../contexts/DialogContext';
import { MobileNavigationProvider } from '../contexts/MobileNavigationContext';
import '../utils/mobileNavigationTest'; // Load test utilities

const Index = () => {
  const [isBooting, setIsBooting] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsBooting(false);
    }, 2000); // 2 second boot sequence

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="h-screen w-screen overflow-hidden">
      {isBooting ? (
        <BootScreen onBootComplete={() => setIsBooting(false)} />
      ) : (
        <MobileNavigationProvider>
          <DialogProvider>
            <Desktop />
          </DialogProvider>
        </MobileNavigationProvider>
      )}
    </div>
  );
};

export default Index;
