/**
 * Test file for icon position management utilities
 * This can be run manually to verify functionality
 */

import {
  getIconPosition,
  updateIconPosition,
  resetIconPositions,
  constrainToDesktop,
  checkIconCollision,
  findValidPosition,
  getAllIconPositions,
  ICON_CONSTANTS,
} from '../iconPositionManager';

// Mock sessionStorage for testing
const mockSessionStorage = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => { store[key] = value; },
    removeItem: (key: string) => { delete store[key]; },
    clear: () => { store = {}; },
  };
})();

// Replace global sessionStorage with mock
Object.defineProperty(global, 'sessionStorage', {
  value: mockSessionStorage,
});

// Mock window dimensions
Object.defineProperty(global, 'window', {
  value: {
    innerWidth: 1024,
    innerHeight: 768,
  },
});

// Test functions
export function testIconPositionManager() {
  console.log('Testing Icon Position Manager...');

  // Test 1: Default positions
  console.log('\n1. Testing default positions:');
  const myComputerPos = getIconPosition('my_computer');
  console.log('My Computer position:', myComputerPos);
  console.log('Expected: { x: 16, y: 16 }');

  // Test 2: Update and retrieve position
  console.log('\n2. Testing position updates:');
  updateIconPosition('my_computer', { x: 100, y: 200 });
  const updatedPos = getIconPosition('my_computer');
  console.log('Updated My Computer position:', updatedPos);
  console.log('Expected: { x: 100, y: 200 }');

  // Test 3: Desktop constraints
  console.log('\n3. Testing desktop constraints:');
  const constrainedPos = constrainToDesktop({ x: 2000, y: 2000 });
  console.log('Constrained position:', constrainedPos);
  console.log('Should be within desktop bounds');

  // Test 4: Collision detection
  console.log('\n4. Testing collision detection:');
  const pos1 = { x: 16, y: 16 };
  const pos2 = { x: 20, y: 20 }; // Close position
  const pos3 = { x: 200, y: 200 }; // Far position
  
  const collision1 = checkIconCollision(pos1, pos2);
  const collision2 = checkIconCollision(pos1, pos3);
  console.log('Close positions collision:', collision1, '(should be true)');
  console.log('Far positions collision:', collision2, '(should be false)');

  // Test 5: Find valid position
  console.log('\n5. Testing valid position finding:');
  const iconIds = ['my_computer', 'projects', 'my_resume'];
  const allIcons = getAllIconPositions(iconIds);
  const validPos = findValidPosition({ x: 16, y: 16 }, allIcons, 'test_icon');
  console.log('Valid position for new icon:', validPos);
  console.log('Should not collide with existing icons');

  // Test 6: Reset positions
  console.log('\n6. Testing position reset:');
  resetIconPositions();
  const resetPos = getIconPosition('my_computer');
  console.log('Reset My Computer position:', resetPos);
  console.log('Expected: { x: 16, y: 16 } (back to default)');

  console.log('\n✅ Icon Position Manager tests completed!');
}

// Export for manual testing
if (typeof window !== 'undefined') {
  (window as any).testIconPositionManager = testIconPositionManager;
  console.log('Icon Position Manager test available as window.testIconPositionManager()');
}
