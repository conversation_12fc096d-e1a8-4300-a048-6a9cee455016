/**
 * Email service for sending contact form submissions
 * Integrates with Cloudflare Worker backend that uses Resend API
 */

export interface ContactFormData {
  name: string;
  fromEmail: string;
  subject: string;
  message: string;
}

export interface EmailResponse {
  success: boolean;
  message?: string;
  error?: string;
  emailId?: string;
}

/**
 * Send contact form email via Cloudflare Worker backend
 */
export async function sendContactEmail(formData: ContactFormData): Promise<EmailResponse> {
  try {
    // Validate form data
    const validation = validateContactForm(formData);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.error,
      };
    }

    // Determine the API endpoint
    // In production, this will be handled by Cloudflare Worker routing
    // For development, you might need to adjust this URL
    const apiUrl = import.meta.env.DEV 
      ? 'http://localhost:8787/api/send-email'  // Local Cloudflare Worker dev server
      : '/api/send-email';  // Production route handled by Cloudflare Worker

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: formData.name.trim(),
        fromEmail: formData.fromEmail.trim(),
        subject: formData.subject.trim(),
        message: formData.message.trim(),
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: EmailResponse = await response.json();
    return result;

  } catch (error) {
    console.error('Email service error:', error);
    
    // Return user-friendly error message
    return {
      success: false,
      error: error instanceof Error 
        ? 'Failed to send email. Please check your connection and try again.'
        : 'An unexpected error occurred. Please try again later.',
    };
  }
}

/**
 * Validate contact form data
 */
function validateContactForm(formData: ContactFormData): { isValid: boolean; error?: string } {
  // Check required fields
  if (!formData.name?.trim()) {
    return { isValid: false, error: 'Name is required.' };
  }

  if (!formData.fromEmail?.trim()) {
    return { isValid: false, error: 'Email address is required.' };
  }

  if (!formData.subject?.trim()) {
    return { isValid: false, error: 'Subject is required.' };
  }

  if (!formData.message?.trim()) {
    return { isValid: false, error: 'Message is required.' };
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(formData.fromEmail.trim())) {
    return { isValid: false, error: 'Please enter a valid email address.' };
  }

  // Check field lengths
  if (formData.name.trim().length > 100) {
    return { isValid: false, error: 'Name must be less than 100 characters.' };
  }

  if (formData.subject.trim().length > 200) {
    return { isValid: false, error: 'Subject must be less than 200 characters.' };
  }

  if (formData.message.trim().length > 5000) {
    return { isValid: false, error: 'Message must be less than 5000 characters.' };
  }

  return { isValid: true };
}


