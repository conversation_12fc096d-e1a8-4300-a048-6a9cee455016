import React, { createContext, useContext, ReactNode } from 'react';
import { useMobileNavigation, MobileNavigationManager } from '../hooks/useMobileNavigation';

interface MobileNavigationContextType extends MobileNavigationManager {
  // Additional context-specific methods can be added here
}

const MobileNavigationContext = createContext<MobileNavigationContextType | null>(null);

interface MobileNavigationProviderProps {
  children: ReactNode;
}

/**
 * Mobile Navigation Provider that manages navigation history for mobile devices
 * Provides back button functionality and navigation state management
 */
export const MobileNavigationProvider: React.FC<MobileNavigationProviderProps> = ({
  children
}) => {
  const navigationManager = useMobileNavigation();

  return (
    <MobileNavigationContext.Provider value={navigationManager}>
      {children}
    </MobileNavigationContext.Provider>
  );
};

/**
 * Hook to access the mobile navigation manager from any component
 * Throws an error if used outside of MobileNavigationProvider
 */
export const useMobileNavigationContext = (): MobileNavigationContextType => {
  const context = useContext(MobileNavigationContext);
  
  if (!context) {
    throw new Error('useMobileNavigationContext must be used within a MobileNavigationProvider');
  }
  
  return context;
};

/**
 * Hook to safely access the mobile navigation manager (returns null if not available)
 * Useful for optional navigation functionality
 */
export const useMobileNavigationSafe = (): MobileNavigationContextType | null => {
  return useContext(MobileNavigationContext);
};
