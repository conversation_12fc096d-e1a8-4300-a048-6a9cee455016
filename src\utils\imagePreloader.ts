/**
 * Utility for preloading images during the Windows XP boot sequence
 * Ensures critical images are cached before the desktop loads
 */

export interface PreloadResult {
  success: boolean;
  url: string;
  error?: string;
}

/**
 * Preload a single image and return a promise that resolves when complete
 */
export const preloadImage = (src: string): Promise<PreloadResult> => {
  return new Promise((resolve) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({ success: true, url: src });
    };
    
    img.onerror = () => {
      resolve({ 
        success: false, 
        url: src, 
        error: 'Failed to load image' 
      });
    };
    
    // Set crossOrigin for external images if needed
    if (src.startsWith('http')) {
      img.crossOrigin = 'anonymous';
    }
    
    img.src = src;
  });
};

/**
 * Preload multiple images concurrently
 */
export const preloadImages = async (sources: string[]): Promise<PreloadResult[]> => {
  const promises = sources.map(src => preloadImage(src));
  return Promise.all(promises);
};

/**
 * Preload critical desktop assets during boot sequence
 */
export const preloadDesktopAssets = async (): Promise<PreloadResult[]> => {
  // Import the Windows XP Bliss background
  const { default: windowsXpBliss } = await import('../assets/windows-xp-bliss.jpg');
  
  // Add other critical images here if needed
  const criticalAssets = [
    windowsXpBliss
  ];
  
  return preloadImages(criticalAssets);
};
