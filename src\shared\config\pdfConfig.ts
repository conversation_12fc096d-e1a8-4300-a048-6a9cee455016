/**
 * Shared PDF configuration for resume/CV functionality
 * Consolidates URLs and provides utilities for both XP and modern themes
 */

// Using the Google Drive file ID from the XP theme "my resume" as the primary source
export const PDF_CONFIG = {
  // Primary Google Drive file ID (from XP theme "my resume")
  FILE_ID: "1ukhF_CodJGn9-GJqkrt56aNnATEBgTIN",

  // Primary Google Drive URL constructed from file ID
  GOOGLE_DRIVE_URL: "https://drive.google.com/file/d/1ukhF_CodJGn9-GJqkrt56aNnATEBgTIN/view",

  // Alternative document ID (from modern theme, now deprecated)
  DOCUMENT_ID: "1WUIFMPyO_Joffy7XLiQsfkKKfBKoWTRq",
} as const;

/**
 * Get the appropriate embed URL for iframe display
 */
export const getEmbedUrl = (url?: string): string => {
  const sourceUrl = url || PDF_CONFIG.GOOGLE_DRIVE_URL;

  if (sourceUrl.includes('docs.google.com/document')) {
    // Extract the document ID from Google Docs URL
    const docId = sourceUrl.match(/\/document\/d\/([a-zA-Z0-9-_]+)/)?.[1];
    return `https://docs.google.com/document/d/${docId}/preview`;
  } else if (sourceUrl.includes('/file/d/')) {
    // Handle Google Drive file links
    const fileId = sourceUrl.match(/\/file\/d\/([a-zA-Z0-9-_]+)/)?.[1];
    return `https://drive.google.com/file/d/${fileId}/preview`;
  }

  return sourceUrl;
};

/**
 * Get the appropriate download URL
 */
export const getDownloadUrl = (url?: string): string => {
  const sourceUrl = url || PDF_CONFIG.GOOGLE_DRIVE_URL;

  if (sourceUrl.includes('docs.google.com/document')) {
    // Extract the document ID from Google Docs URL
    const docId = sourceUrl.match(/\/document\/d\/([a-zA-Z0-9-_]+)/)?.[1];
    return `https://docs.google.com/document/d/${docId}/export?format=pdf`;
  } else if (sourceUrl.includes('/file/d/')) {
    // Handle Google Drive file links
    const fileId = sourceUrl.match(/\/file\/d\/([a-zA-Z0-9-_]+)/)?.[1];
    return `https://drive.google.com/uc?export=download&id=${fileId}`;
  }

  return sourceUrl;
};

/**
 * Get the primary embed URL using the configured document
 */
export const getPrimaryEmbedUrl = (): string => {
  return getEmbedUrl(PDF_CONFIG.GOOGLE_DRIVE_URL);
};

/**
 * Get the primary download URL using the configured document
 */
export const getPrimaryDownloadUrl = (): string => {
  return getDownloadUrl(PDF_CONFIG.GOOGLE_DRIVE_URL);
};
