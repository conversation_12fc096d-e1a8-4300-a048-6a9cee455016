
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Monitor, ChevronUp, ChevronDown, Activity, Clock, CheckCircle, XCircle } from 'lucide-react';

interface ApiCall {
  id: string;
  endpoint: string;
  method: string;
  status: number;
  timestamp: Date;
  duration: number;
  section: string;
}

const ApiMonitor = () => {
  const [apiCalls, setApiCalls] = useState<ApiCall[]>([]);
  const [isMinimized, setIsMinimized] = useState(true);

  useEffect(() => {
    const handleFetch = () => {
      // This is a simplified version - in a real app, you'd intercept actual fetch calls
      const mockCall: ApiCall = {
        id: Math.random().toString(36).substr(2, 9),
        endpoint: '/api/test',
        method: 'GET',
        status: 200,
        timestamp: new Date(),
        duration: Math.floor(Math.random() * 500) + 100,
        section: 'test'
      };
      
      setApiCalls(prev => [mockCall, ...prev.slice(0, 9)]);
    };

    // Listen for custom events (you'd set these up in your actual API calls)
    window.addEventListener('api-call', handleFetch);
    
    return () => window.removeEventListener('api-call', handleFetch);
  }, []);

  const getStatusColor = (status: number) => {
    if (status >= 200 && status < 300) return 'text-green-500';
    if (status >= 400) return 'text-red-500';
    return 'text-yellow-500';
  };

  const getStatusIcon = (status: number) => {
    if (status >= 200 && status < 300) return <CheckCircle className="w-3 h-3" />;
    if (status >= 400) return <XCircle className="w-3 h-3" />;
    return <Clock className="w-3 h-3" />;
  };

  // Reordered sections: profile, projects, skills, experience
  const sectionOrder = ['profile', 'projects', 'skills', 'experience'];
  const sortedCalls = apiCalls.sort((a, b) => {
    const aIndex = sectionOrder.indexOf(a.section);
    const bIndex = sectionOrder.indexOf(b.section);
    return aIndex - bIndex;
  });

  return (
    <div className="fixed bottom-6 right-6 z-40 w-80 max-h-96">
      <Card className="bg-slate-900/95 dark:bg-slate-800/95 backdrop-blur-xl border-slate-700/50 dark:border-slate-600/50 shadow-2xl">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-slate-100 dark:text-slate-200 text-sm font-medium flex items-center gap-2">
              <Monitor className="w-4 h-4" />
              API Monitor
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                <span className="text-xs text-slate-400 dark:text-slate-500">Live</span>
              </div>
            </CardTitle>
            <Button
              onClick={() => setIsMinimized(!isMinimized)}
              variant="ghost"
              size="sm"
              className="text-slate-400 dark:text-slate-500 hover:text-slate-200 dark:hover:text-slate-300 h-6 w-6 p-0"
            >
              {isMinimized ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            </Button>
          </div>
        </CardHeader>

        {!isMinimized && (
          <CardContent className="pt-0">
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {sortedCalls.length === 0 ? (
                <div className="text-center py-4">
                  <Activity className="w-8 h-8 text-slate-600 dark:text-slate-500 mx-auto mb-2 opacity-50" />
                  <p className="text-xs text-slate-500 dark:text-slate-400">No API calls yet</p>
                  <p className="text-xs text-slate-600 dark:text-slate-500">Scroll through sections to trigger calls</p>
                </div>
              ) : (
                sortedCalls.map((call) => (
                  <div key={call.id} className="flex items-center justify-between p-2 rounded-lg bg-slate-800/50 dark:bg-slate-700/50 border border-slate-700/30 dark:border-slate-600/30">
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      {getStatusIcon(call.status)}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="text-xs font-mono text-slate-300 dark:text-slate-400 truncate">
                            {call.endpoint}
                          </span>
                          <Badge variant="outline" className="text-xs px-1 py-0 text-slate-400 dark:text-slate-500 border-slate-600 dark:border-slate-500">
                            {call.method}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <span className={`text-xs font-medium ${getStatusColor(call.status)}`}>
                            {call.status}
                          </span>
                          <span className="text-xs text-slate-500 dark:text-slate-400">
                            {call.duration}ms
                          </span>
                          <span className="text-xs text-slate-600 dark:text-slate-500">
                            {call.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
};

export default ApiMonitor;
