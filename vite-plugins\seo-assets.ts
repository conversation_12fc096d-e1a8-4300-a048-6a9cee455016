import { Plugin } from 'vite';
import fs from 'fs';
import path from 'path';

export function seoAssetsPlugin(): Plugin {
  return {
    name: 'seo-assets',
    generateBundle(options, bundle) {
      const seoAssets = [
        'robots.txt',
        'sitemap.xml',
        'favicon.ico',
        'windows-logo.png'
      ];

      seoAssets.forEach(asset => {
        const assetPath = path.resolve('public', asset);
        if (fs.existsSync(assetPath)) {
          const content = fs.readFileSync(assetPath);
          this.emitFile({
            type: 'asset',
            fileName: asset,
            source: content
          });
        }
      });
    },
    transformIndexHtml: {
      enforce: 'post',
      transform(html, context) {
        if (context.bundle) {
          // Find the Windows XP Bliss background asset in the bundle
          let blissImagePath = '';
          for (const [fileName] of Object.entries(context.bundle)) {
            if (fileName.includes('windows-xp-bliss') && (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg'))) {
              blissImagePath = `/${fileName}`;
              break;
            }
          }

          const preloadHints = `
    <!-- Preload critical resources for better SEO performance -->
    <link rel="preload" href="/windows-logo.png" as="image" type="image/png">${blissImagePath ? `
    <!-- Preload Windows XP Bliss background for instant desktop loading -->
    <link rel="preload" href="${blissImagePath}" as="image" type="image/jpeg">` : ''}
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">`;

          html = html.replace('</head>', `${preloadHints}\n  </head>`);

          html = html.replace('<html', '<html lang="en"');

          if (!html.includes('name="viewport"')) {
            const viewportMeta = '<meta name="viewport" content="width=device-width, initial-scale=1.0" />';
            html = html.replace('<head>', `<head>\n    ${viewportMeta}`);
          }
        }

        return html;
      }
    }
  };
}
