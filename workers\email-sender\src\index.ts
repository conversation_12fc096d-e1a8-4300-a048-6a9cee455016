/**
 * <PERSON><PERSON>lare Worker for sending emails via Resend API
 * This worker handles contact form submissions securely without exposing API keys to the client
 */

import { Resend } from 'resend';

export interface Env {
  RESEND_API_KEY: string;
}

interface ContactFormData {
  name: string;
  fromEmail: string;
  subject: string;
  message: string;
}

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
};

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: corsHeaders,
      });
    }

    // Only allow POST requests
    if (request.method !== 'POST') {
      return new Response('Method not allowed', {
        status: 405,
        headers: corsHeaders,
      });
    }

    try {
      // Parse the request body
      const formData: ContactFormData = await request.json();

      // Validate required fields
      if (!formData.name || !formData.fromEmail || !formData.subject || !formData.message) {
        return new Response(
          JSON.stringify({ 
            success: false, 
            error: 'Missing required fields: name, fromEmail, subject, message' 
          }),
          {
            status: 400,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders,
            },
          }
        );
      }

      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.fromEmail)) {
        return new Response(
          JSON.stringify({ 
            success: false, 
            error: 'Invalid email address' 
          }),
          {
            status: 400,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders,
            },
          }
        );
      }

      // Initialize Resend with API key from environment
      const resend = new Resend(env.RESEND_API_KEY);

      // Create email content
      const emailHtml = `
        <div style="font-family: 'Tahoma', sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(to right, #1e40af, #3b82f6); color: white; padding: 15px; border-radius: 5px 5px 0 0;">
            <h2 style="margin: 0; font-size: 18px;">📧 New Contact Form Submission</h2>
          </div>
          
          <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-top: none; padding: 20px; border-radius: 0 0 5px 5px;">
            <div style="margin-bottom: 15px;">
              <strong style="color: #374151;">Name:</strong>
              <div style="background: white; padding: 8px; border: 1px solid #d1d5db; border-radius: 3px; margin-top: 5px;">
                ${formData.name}
              </div>
            </div>
            
            <div style="margin-bottom: 15px;">
              <strong style="color: #374151;">Email:</strong>
              <div style="background: white; padding: 8px; border: 1px solid #d1d5db; border-radius: 3px; margin-top: 5px;">
                ${formData.fromEmail}
              </div>
            </div>
            
            <div style="margin-bottom: 15px;">
              <strong style="color: #374151;">Subject:</strong>
              <div style="background: white; padding: 8px; border: 1px solid #d1d5db; border-radius: 3px; margin-top: 5px;">
                ${formData.subject}
              </div>
            </div>
            
            <div style="margin-bottom: 15px;">
              <strong style="color: #374151;">Message:</strong>
              <div style="background: white; padding: 12px; border: 1px solid #d1d5db; border-radius: 3px; margin-top: 5px; white-space: pre-wrap;">
                ${formData.message}
              </div>
            </div>
            
            <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e2e8f0; font-size: 12px; color: #6b7280;">
              <p style="margin: 0;">This message was sent from your Windows XP Portfolio contact form.</p>
              <p style="margin: 5px 0 0 0;">Timestamp: ${new Date().toLocaleString()}</p>
            </div>
          </div>
        </div>
      `;

      // Send email via Resend
      const emailResult = await resend.emails.send({
        from: 'Portfolio Contact <<EMAIL>>',
        to: ['<EMAIL>'],
        replyTo: formData.fromEmail,
        subject: `Portfolio Contact: ${formData.subject}`,
        html: emailHtml,
      });

      if (emailResult.error) {
        console.error('Resend error:', emailResult.error);
        return new Response(
          JSON.stringify({ 
            success: false, 
            error: 'Failed to send email. Please try again later.' 
          }),
          {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders,
            },
          }
        );
      }

      // Success response
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'Email sent successfully!',
          emailId: emailResult.data?.id 
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders,
          },
        }
      );

    } catch (error) {
      console.error('Worker error:', error);
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Internal server error. Please try again later.' 
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders,
          },
        }
      );
    }
  },
};
