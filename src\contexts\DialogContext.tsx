import React, { createContext, useContext, useEffect, ReactNode, useRef } from 'react';
import { 
  useDialogManager, 
  DialogType, 
  DialogState, 
  DialogManagerOptions,
  setGlobalDialogManager 
} from '../hooks/useDialogManager';

interface DialogContextType {
  dialogs: DialogState[];
  openDialog: (type: DialogType, data?: any, options?: { allowMultiple?: boolean; id?: string }) => string | null;
  closeDialog: (identifier: string | DialogType) => void;
  closeAllDialogs: () => void;
  bringToFront: (identifier: string | DialogType) => void;
  isDialogOpen: (type: DialogType) => boolean;
  getDialog: (type: DialogType) => DialogState | undefined;
  getDialogCount: (type?: DialogType) => number;
  maxZIndex: number;
}

const DialogContext = createContext<DialogContextType | null>(null);

interface DialogProviderProps {
  children: ReactNode;
  options?: DialogManagerOptions;
}

/**
 * Dialog provider that manages global dialog state and prevents multiple instances
 * Integrates with the Windows XP desktop environment
 */
export const DialogProvider: React.FC<DialogProviderProps> = ({
  children,
  options = {}
}) => {
  const hasShownWelcomeRef = useRef(false);
  const dialogManager = useDialogManager({
    preventMultipleInstances: true,
    maxDialogs: 10,
    baseZIndex: 9000,
    ...options
  });

  // Set the global dialog manager instance for use outside React components
  useEffect(() => {
    setGlobalDialogManager(dialogManager);

    return () => {
      setGlobalDialogManager(null);
    };
  }, [dialogManager]);

  // Show welcome dialog on first load - using custom events to communicate with Desktop
  useEffect(() => {
    if (!hasShownWelcomeRef.current) {
      hasShownWelcomeRef.current = true;

      // Use a minimal delay to ensure everything is initialized
      const timer = setTimeout(() => {
        dialogManager.openDialog('welcome', {
          quickActions: [
            {
              icon: <span className="text-red-600">📄</span>,
              label: 'View Resume',
              onClick: () => {
                // Dispatch custom event that Desktop component can listen to
                window.dispatchEvent(new CustomEvent('openWindow', {
                  detail: { iconId: 'my_resume', iconName: 'my_resume.pdf' }
                }));
              }
            },
            {
              icon: <span className="text-yellow-600">📁</span>,
              label: 'See My Projects',
              onClick: () => {
                window.dispatchEvent(new CustomEvent('openWindow', {
                  detail: { iconId: 'projects', iconName: 'My Projects' }
                }));
              }
            },
            {
              icon: <span className="text-blue-600">📧</span>,
              label: 'Contact Info',
              onClick: () => {
                window.dispatchEvent(new CustomEvent('openWindow', {
                  detail: { iconId: 'contact', iconName: 'Contact Info' }
                }));
              }
            }
          ]
        });
      }, 100); // Minimal delay for initialization

      return () => clearTimeout(timer);
    }
  }, []); // Empty dependency array - runs only once

  return (
    <DialogContext.Provider value={dialogManager}>
      {children}
    </DialogContext.Provider>
  );
};

/**
 * Hook to access the dialog manager from any component
 * Throws an error if used outside of DialogProvider
 */
export const useDialog = (): DialogContextType => {
  const context = useContext(DialogContext);
  
  if (!context) {
    throw new Error('useDialog must be used within a DialogProvider');
  }
  
  return context;
};

/**
 * Hook to safely access the dialog manager (returns null if not available)
 * Useful for optional dialog functionality
 */
export const useDialogSafe = (): DialogContextType | null => {
  return useContext(DialogContext);
};

/**
 * Higher-order component that provides dialog management to wrapped components
 */
export const withDialogManager = <P extends object>(
  Component: React.ComponentType<P & { dialogManager: DialogContextType }>
) => {
  return (props: P) => {
    const dialogManager = useDialog();
    return <Component {...props} dialogManager={dialogManager} />;
  };
};

/**
 * Custom hook for managing a specific dialog type
 * Provides convenient methods for a single dialog type
 */
export const useSpecificDialog = (dialogType: DialogType) => {
  const { openDialog, closeDialog, isDialogOpen, getDialog, bringToFront } = useDialog();
  
  const open = (data?: any, options?: { allowMultiple?: boolean; id?: string }) => {
    return openDialog(dialogType, data, options);
  };
  
  const close = () => {
    closeDialog(dialogType);
  };
  
  const isOpen = () => {
    return isDialogOpen(dialogType);
  };
  
  const getDialogData = () => {
    return getDialog(dialogType);
  };
  
  const bringToFrontDialog = () => {
    bringToFront(dialogType);
  };
  
  return {
    open,
    close,
    isOpen,
    getDialogData,
    bringToFront: bringToFrontDialog,
    dialogType
  };
};
