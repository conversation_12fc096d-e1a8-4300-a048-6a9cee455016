name = "portfolio-email-sender"
main = "src/index.ts"
compatibility_date = "2024-01-01"

# Environment variables (set these in Cloudflare dashboard or via wrangler secret)
[env.production.vars]
# RESEND_API_KEY will be set as a secret via: wrangler secret put RESEND_API_KEY

[env.development.vars]
# For local development, you can set this in .dev.vars file

# Worker settings
[build]
command = "npm run build"

# Routes (update this to match your domain)
[[env.production.routes]]
pattern = "markoverano.dev/api/send-email"
zone_name = "markoverano.dev"

# For development/preview
[[env.development.routes]]
pattern = "*.markoverano.dev/api/send-email"
