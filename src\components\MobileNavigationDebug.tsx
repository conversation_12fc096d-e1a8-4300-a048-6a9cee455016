import React, { useState } from 'react';
import { useIsMobile } from '../hooks/use-mobile';
import { useMobileNavigationSafe } from '../contexts/MobileNavigationContext';

/**
 * Debug component for testing mobile navigation functionality
 * Only visible on mobile devices and when debug mode is enabled
 */
const MobileNavigationDebug: React.FC = () => {
  const isMobile = useIsMobile();
  const mobileNavigation = useMobileNavigationSafe();
  const [isVisible, setIsVisible] = useState(false);

  // Don't render on desktop or if navigation context is not available
  if (!isMobile || !mobileNavigation) {
    return null;
  }

  const { navigationHistory, currentState, canGoBack, isAtHome } = mobileNavigation;

  return (
    <>
      {/* Debug Toggle Button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-20 right-4 z-50 bg-gray-800 text-white p-2 rounded text-xs"
        style={{ fontSize: '10px' }}
      >
        {isVisible ? 'Hide' : 'Debug'}
      </button>

      {/* Debug Panel */}
      {isVisible && (
        <div className="fixed bottom-24 right-4 z-50 bg-black bg-opacity-90 text-white p-3 rounded max-w-xs text-xs overflow-auto max-h-64">
          <h3 className="font-bold mb-2">Mobile Navigation Debug</h3>
          
          <div className="mb-2">
            <strong>Status:</strong>
            <div>Can Go Back: {canGoBack ? 'Yes' : 'No'}</div>
            <div>Is At Home: {isAtHome ? 'Yes' : 'No'}</div>
            <div>History Length: {navigationHistory.length}</div>
          </div>

          <div className="mb-2">
            <strong>Current State:</strong>
            {currentState ? (
              <div className="ml-2">
                <div>Type: {currentState.type}</div>
                {currentState.windowId && <div>Window: {currentState.windowId}</div>}
                {currentState.windowName && <div>Name: {currentState.windowName}</div>}
                {currentState.dialogType && <div>Dialog: {currentState.dialogType}</div>}
              </div>
            ) : (
              <div>None</div>
            )}
          </div>

          <div className="mb-2">
            <strong>History:</strong>
            <div className="ml-2 max-h-32 overflow-y-auto">
              {navigationHistory.map((state, index) => (
                <div key={state.id} className={`text-xs ${index === navigationHistory.length - 1 ? 'font-bold' : ''}`}>
                  {index + 1}. {state.type}
                  {state.windowId && ` (${state.windowId})`}
                  {state.dialogType && ` [${state.dialogType}]`}
                </div>
              ))}
            </div>
          </div>

          <div className="flex gap-2">
            <button
              onClick={() => window.history.back()}
              disabled={!canGoBack}
              className="bg-blue-600 disabled:bg-gray-600 px-2 py-1 rounded text-xs"
            >
              Back
            </button>
            <button
              onClick={() => mobileNavigation.clearHistory()}
              className="bg-red-600 px-2 py-1 rounded text-xs"
            >
              Clear
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default MobileNavigationDebug;
