import { useEffect } from 'react';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  canonical?: string;
  ogImage?: string;
}

const SEOHead = ({ 
  title = "markoverano - Software Engineer Portfolio",
  description = "<PERSON> (markoverano) - Experienced software engineer specializing in full-stack development, .NET technologies, React, Angular, Node.js, and modern web applications. Windows XP themed portfolio showcasing 10+ years of professional experience.",
  keywords = "mark<PERSON><PERSON>, marko verano, mark jovet verano, software engineer, full stack developer, .NET developer, React developer, Angular developer, Node.js developer, web development, mobile development, portfolio, Windows XP, nostalgic design",
  canonical = "https://markoverano.dev/",
  ogImage = "https://markoverano.dev/favicon.ico"
}: SEOHeadProps) => {
  
  useEffect(() => {
    document.title = title;
    
    const updateMetaTag = (name: string, content: string, property?: boolean) => {
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`;
      let meta = document.querySelector(selector) as HTMLMetaElement;
      
      if (!meta) {
        meta = document.createElement('meta');
        if (property) {
          meta.setAttribute('property', name);
        } else {
          meta.setAttribute('name', name);
        }
        document.head.appendChild(meta);
      }
      
      meta.setAttribute('content', content);
    };
    
    const updateCanonical = (href: string) => {
      let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
      
      if (!canonical) {
        canonical = document.createElement('link');
        canonical.setAttribute('rel', 'canonical');
        document.head.appendChild(canonical);
      }
      
      canonical.setAttribute('href', href);
    };
    
    updateMetaTag('description', description);
    updateMetaTag('keywords', keywords);
    updateMetaTag('og:title', title, true);
    updateMetaTag('og:description', description, true);
    updateMetaTag('og:url', canonical, true);
    updateMetaTag('og:image', ogImage, true);
    updateMetaTag('twitter:title', title, true);
    updateMetaTag('twitter:description', description, true);
    updateMetaTag('twitter:image', ogImage, true);
    
    updateCanonical(canonical);
    
    const addStructuredData = () => {
      const existingScript = document.querySelector('script[type="application/ld+json"]');
      if (existingScript) {
        return;
      }
      
      const structuredData = {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": title,
        "description": description,
        "url": canonical,
        "author": {
          "@type": "Person",
          "name": "Mark Jovet Verano",
          "alternateName": "markoverano"
        },
        "mainEntity": {
          "@type": "Person",
          "name": "Mark Jovet Verano",
          "alternateName": "markoverano",
          "jobTitle": "Software Engineer",
          "url": "https://markoverano.dev"
        }
      };
      
      const script = document.createElement('script');
      script.type = 'application/ld+json';
      script.textContent = JSON.stringify(structuredData);
      document.head.appendChild(script);
    };
    
    addStructuredData();
    
  }, [title, description, keywords, canonical, ogImage]);
  
  return null;
};

export default SEOHead;
