
import StartMenuItem from './StartMenuItem';
import AllProgramsMenu from '../AllProgramsMenu';
import { ChevronRight, Globe, Mail } from 'lucide-react';

interface MenuItem {
    id: string;
    name: string;
    icon: string;
}

interface StartMenuLeftColumnProps {
    menuItems: MenuItem[];
    handleOpenWindow: (id: string, name: string) => void;
    showAllPrograms: boolean;
    setShowAllPrograms: (show: boolean) => void;
}

const StartMenuLeftColumn = ({ menuItems, handleOpenWindow, showAllPrograms, setShowAllPrograms }: StartMenuLeftColumnProps) => {
    const portfolioMap = menuItems.reduce((acc, item) => {
        acc[item.name.toLowerCase()] = item;
        return acc;
    }, {} as Record<string, MenuItem>);

    const projectsItem = portfolioMap['projects'];
    const contactItem = portfolioMap['contact'];
    const otherMenuItems = menuItems.filter(item => !['projects', 'contact'].includes(item.name.toLowerCase()));

    return (
        <div className="w-1/2 bg-white p-2 flex flex-col border-r border-gray-300">
            <div className="space-y-1">
                {/* <StartMenuItem
                    icon={<Globe size={32} />}
                    name="Internet"
                    subtext="Projects"
                    bold
                    onClick={() => projectsItem && handleOpenWindow(projectsItem.id, projectsItem.name)}
                />
                <StartMenuItem
                    icon={<Mail size={32} />}
                    name="E-mail"
                    subtext="Contact"
                    bold
                    onClick={() => contactItem && handleOpenWindow(contactItem.id, contactItem.name)}
                />
                <div className="border-t border-gray-300 my-1 !mt-2 !mb-2"></div> */}
                {otherMenuItems.map((item) => (
                    <StartMenuItem
                        key={item.id}
                        icon={<span className="text-2xl">{item.icon}</span>}
                        name={item.name}
                        onClick={() => handleOpenWindow(item.id, item.name)}
                    />
                ))}
            </div>

            <div className="!mt-auto pt-2 border-t border-gray-300 relative" onMouseLeave={() => setShowAllPrograms(false)}>
                <button
                    className="w-full flex items-center justify-between px-3 py-1.5 hover:bg-[#316AC5] hover:text-white text-left text-sm rounded-sm group transition-colors duration-150"
                    onMouseEnter={() => setShowAllPrograms(true)}
                >
                    <div className="flex items-center space-x-3">
                        <span className="font-bold text-black group-hover:text-white">All Programs</span>
                    </div>
                    <ChevronRight size={20} className="text-black group-hover:text-white" />
                </button>
                {showAllPrograms && <AllProgramsMenu />}
            </div>
        </div>
    );
};

export default StartMenuLeftColumn;
