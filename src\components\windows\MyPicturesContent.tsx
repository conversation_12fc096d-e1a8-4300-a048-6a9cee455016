
import { Image as ImageIcon } from 'lucide-react';
import FolderWindow from '@shared/components/FolderWindow';

interface MyPicturesContentProps {
  onOpenWindow?: (id: string, name: string) => void;
}

const pictureData = [
  {
    src: '/android-chrome-192x192.png',
    alt: 'Android Chrome 192x192',
    title: 'android-chrome-192x192.png',
    size: '10 KB'
  },
  {
    src: '/android-chrome-512x512.png',
    alt: 'Android Chrome 512x512',
    title: 'android-chrome-512x512.png',
    size: '50 KB'
  },
  {
    src: '/apple-touch-icon.png',
    alt: 'Apple Touch Icon',
    title: 'apple-touch-icon.png',
    size: '5 KB'
  },
  {
    src: '/chromepanion-b.png',
    alt: 'Chromepanion B',
    title: 'chromepanion-b.png',
    size: '1.2 MB'
  },
  {
    src: '/chromepanion.png',
    alt: 'Chromepanion',
    title: 'chromepanion.png',
    size: '1.5 MB'
  },
  {
    src: '/favicon-16x16.png',
    alt: 'Favicon 16x16',
    title: 'favicon-16x16.png',
    size: '1 KB'
  },
  {
    src: '/favicon-32x32.png',
    alt: 'Favicon 32x32',
    title: 'favicon-32x32.png',
    size: '2 KB'
  },
  {
    src: '/projects/z-task.png',
    alt: 'Z-Task',
    title: 'z-task.png',
    size: '300 KB'
  },
  {
    src: '/windows-logo.png',
    alt: 'Windows Logo',
    title: 'windows-logo.png',
    size: '20 KB'
  }
];



const MyPicturesContent = ({ onOpenWindow }: MyPicturesContentProps) => {
  // Custom content renderer for pictures to maintain the original layout
  const renderPictureContent = () => (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {pictureData.map((pic, index) => (
        <div key={index} className="group cursor-pointer">
          <div className="bg-gray-100 border rounded overflow-hidden aspect-square flex items-center justify-center group-hover:shadow-lg transition-shadow">
            <img src={pic.src} alt={pic.alt} className="w-full h-full object-cover" />
          </div>
          <div className="text-center mt-2 text-xs">
            <p className="font-semibold truncate">{pic.title}</p>
            <p className="text-gray-500">{pic.size}</p>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <FolderWindow
      title="My Pictures"
      icon={<ImageIcon size={16} className="text-blue-600" />}
      customContent={renderPictureContent()}
      onOpenWindow={onOpenWindow}
      showSidebar={false}
      variant="default"
      onBackClick={() => console.log('Back to My Documents')}
      onFoldersClick={() => console.log('Toggle folders')}
    />
  );
};

export default MyPicturesContent;
