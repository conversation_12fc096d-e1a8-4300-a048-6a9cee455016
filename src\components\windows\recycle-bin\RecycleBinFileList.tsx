
import { FileIcon } from '../../../lib/file-icons';
import { RecycleBinFile } from './recycleBinData';

interface RecycleBinFileListProps {
  files: RecycleBinFile[];
  selectedFile: string | null;
  onFileSelect: (fileId: string) => void;
}

const RecycleBinFileList = ({ files, selectedFile, onFileSelect }: RecycleBinFileListProps) => {
  return (
    <div className="h-full flex flex-col">
      {/* Header section - fixed with Windows XP styling */}
      <div className="bg-gradient-to-b from-blue-50 to-blue-100 border-b border-gray-300 p-3 flex-shrink-0 overflow-hidden">
        <h3 className="font-bold text-blue-800 text-sm truncate">Recycle Bin Tasks</h3>
        <div className="mt-2 space-y-1">
          <button className="w-full text-left text-xs text-blue-600 hover:text-blue-800 hover:underline cursor-pointer truncate">
            🗑️ Empty the Recycle Bin
          </button>
          <button className="w-full text-left text-xs text-blue-600 hover:text-blue-800 hover:underline cursor-pointer truncate">
            ↩️ Restore all items
          </button>
        </div>
      </div>

      {/* File count section */}
      <div className="bg-white border-b border-gray-300 p-2 flex-shrink-0 overflow-hidden">
        <p className="text-xs text-gray-600 font-medium truncate">{files.length} item{files.length !== 1 ? 's' : ''}</p>
      </div>

      {/* Scrollable file list */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden">
        {files.map((file) => (
          <div
            key={file.id}
            className={`p-3 border-b border-gray-200 cursor-pointer hover:bg-blue-50 transition-colors ${
              selectedFile === file.id ? 'bg-blue-100 border-l-4 border-l-blue-500' : ''
            }`}
            onClick={() => onFileSelect(file.id)}
          >
            <div className="flex items-center space-x-2 mb-1 min-w-0">
              <FileIcon filename={file.name} className="h-5 w-5 text-gray-700 flex-shrink-0" />
              <span className="text-sm font-medium truncate min-w-0 flex-1">{file.name}</span>
            </div>
            <div className="text-xs text-gray-500 pl-7 min-w-0">
              <div className="truncate">{file.type}</div>
              <div className="truncate">{file.size} • {file.modified}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecycleBinFileList;
