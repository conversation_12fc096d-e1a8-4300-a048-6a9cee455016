
import { useState } from 'react';
import { recycleBinFiles } from './recycle-bin/recycleBinData';
import RecycleBinFileList from './recycle-bin/RecycleBinFileList';
import RecycleBinFileViewer from './recycle-bin/RecycleBinFileViewer';
import RecycleBinEmptyState from './recycle-bin/RecycleBinEmptyState';
import WindowsFolderHeader from './WindowsFolderHeader';
import { Trash2 } from 'lucide-react';

interface RecycleBinContentProps {
  onOpenWindow?: (id: string, name: string) => void;
}

const RecycleBinContent = ({ onOpenWindow }: RecycleBinContentProps) => {
  const [selectedFile, setSelectedFile] = useState<string | null>(null);

  const selectedFileData = recycleBinFiles.find(f => f.id === selectedFile);

  return (
    <div className="h-full flex flex-col bg-white font-tahoma">
      <WindowsFolderHeader
        title="Recycle Bin"
        icon={<Trash2 size={16} className="text-blue-600" />}
        variant="default"
        onBackClick={() => console.log('Back to Desktop')}
        onFoldersClick={() => console.log('Toggle folders')}
        onOpenWindow={onOpenWindow}
      />
      <div className="flex-1 flex bg-gray-50 overflow-hidden">
        {/* Left Sidebar - File List with independent scrollbar */}
        <div className="w-64 flex-shrink-0 border-r border-gray-300 flex flex-col bg-white overflow-x-hidden">
          <RecycleBinFileList
            files={recycleBinFiles}
            selectedFile={selectedFile}
            onFileSelect={setSelectedFile}
          />
        </div>

        {/* Main Content Area with independent scrollbar */}
        <div className="flex-1 flex flex-col overflow-y-auto">
          {selectedFile && selectedFileData ? (
            <RecycleBinFileViewer file={selectedFileData} />
          ) : (
            <RecycleBinEmptyState />
          )}
        </div>
      </div>
    </div>
  );
};

export default RecycleBinContent;
