import { useRef, useCallback, useState, useEffect } from 'react';

interface UsePdfIframeResizeOptions {
  updateWindowSize?: (windowId: string, size: { width: number; height: number }) => void;
  windowId?: string;
  onLoadingComplete?: () => void;
}

interface UsePdfIframeResizeReturn {
  iframeRef: React.RefObject<HTMLIFrameElement>;
  isLoading: boolean;
  loadingProgress: number;
  isVisible: boolean;
  handleIframeLoad: () => void;
  resetResize: () => void;
}

/**
 * Shared hook for handling PDF iframe resize functionality
 * Fixes zoom/display issues by programmatically triggering window resize events
 */
export const usePdfIframeResize = (options: UsePdfIframeResizeOptions = {}): UsePdfIframeResizeReturn => {
  const { updateWindowSize, windowId, onLoadingComplete } = options;

  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const hasResizedRef = useRef(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoadingProgress(20);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  // Reset the resize flag when component unmounts or reloads
  useEffect(() => {
    return () => {
      hasResizedRef.current = false;
    };
  }, []);

  const handleIframeLoad = useCallback(() => {
    // Prevent infinite loops by checking if resize has already been executed
    if (hasResizedRef.current) {
      return;
    }
    hasResizedRef.current = true;

    setLoadingProgress(30);

    if (updateWindowSize && windowId && iframeRef.current) {
      // XP theme specific resize logic
      requestAnimationFrame(() => {
        setLoadingProgress(60);

        setTimeout(() => {
          if (updateWindowSize && windowId && iframeRef.current) {
            const windowElement = iframeRef.current.closest('.xp-window') as HTMLElement;
            if (windowElement) {
              const currentWidth = windowElement.offsetWidth;
              const currentHeight = windowElement.offsetHeight;

              if (currentWidth > 0 && currentHeight > 0) {
                setLoadingProgress(80);

                // First resize - trigger recalculation
                updateWindowSize(windowId, {
                  width: currentWidth + 1,
                  height: currentHeight + 1
                });

                setTimeout(() => {
                  if (updateWindowSize && windowId) {
                    // Restore original size
                    updateWindowSize(windowId, {
                      width: currentWidth,
                      height: currentHeight
                    });
                  }
                  setLoadingProgress(90);
                }, 100);

                // Additional resize for iframe-specific issues
                setTimeout(() => {
                  if (updateWindowSize && windowId && iframeRef.current) {
                    const iframe = iframeRef.current;
                    if (iframe.offsetHeight > 0 && iframe.offsetWidth > 0) {
                      // Second resize cycle
                      updateWindowSize(windowId, {
                        width: currentWidth + 1,
                        height: currentHeight + 1
                      });
                      setTimeout(() => {
                        if (updateWindowSize && windowId) {
                          updateWindowSize(windowId, {
                            width: currentWidth,
                            height: currentHeight
                          });
                        }
                        completeLoading();
                      }, 50);
                    }
                  } else {
                    completeLoading();
                  }
                }, 500);
              }
            }
          }
        }, 200);
      });
    } else {
      // Modern theme or no window resize functionality
      setTimeout(() => {
        // Safe resize handling for modern themes and Google Drive PDFs
        if (iframeRef.current) {
          const iframe = iframeRef.current;

          // Strategy 1: Dispatch window resize event
          window.dispatchEvent(new Event('resize'));

          // Strategy 2: Force iframe reflow by temporarily hiding/showing
          const currentDisplay = iframe.style.display;
          iframe.style.display = 'none';
          iframe.offsetHeight; // Force reflow
          iframe.style.display = currentDisplay;

          // Strategy 3: Force container recalculation
          const container = iframe.parentElement;
          if (container) {
            const containerDisplay = container.style.display;
            container.style.display = 'none';
            container.offsetHeight; // Force reflow
            container.style.display = containerDisplay;
          }

          // Additional resize attempt with different timing
          setTimeout(() => {
            if (iframeRef.current) {
              window.dispatchEvent(new Event('resize'));
            }
          }, 200);
        }
        completeLoading();
      }, 400);
    }
  }, [updateWindowSize, windowId]);

  const completeLoading = useCallback(() => {
    setLoadingProgress(100);
    setTimeout(() => {
      setIsVisible(false);
      setTimeout(() => {
        setIsLoading(false);
        onLoadingComplete?.();
      }, 300);
    }, 200);
  }, [onLoadingComplete]);

  const resetResize = useCallback(() => {
    hasResizedRef.current = false;
    setIsLoading(true);
    setLoadingProgress(0);
    setIsVisible(true);
  }, []);

  return {
    iframeRef,
    isLoading,
    loadingProgress,
    isVisible,
    handleIframeLoad,
    resetResize,
  };
};
