
import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { LucideIcon, Code, ExternalLink } from 'lucide-react';
import { useScrollReveal, useStaggeredScrollReveal } from '../hooks/useScrollReveal';

interface ProjectItem {
  text: string;
  url?: string;
  isClickable: boolean;
}

interface Project {
  id: number;
  title: string;
  description: string;
  icon: LucideIcon;
  items: ProjectItem[];
  color: string;
}

interface ProjectsSectionProps {
  projects: Project[];
}

const ProjectsSection = React.forwardRef<HTMLDivElement, ProjectsSectionProps>(
  ({ projects }, ref) => {
    // Scroll reveal hooks with very gradual timing for smooth flow
    const titleRef = useScrollReveal({ animationClass: 'animate-fade-in-up', delay: 200 });
    const projectsGridRef = useStaggeredScrollReveal(projects.length, {
      animationClass: 'animate-fade-in-up',
      delay: 600,
      staggerDelay: 400
    });

    return (
      <section ref={ref} data-section="projects" className="scroll-mt-28 md:scroll-mt-32">
        <div ref={titleRef} className="text-center mb-8 md:mb-12">
          <h2 className="text-2xl md:text-3xl font-light text-gray-800 dark:text-white mb-4 tracking-tight">
            Side Projects
          </h2>
          <div className="w-16 h-1 bg-gradient-to-r from-gray-500 to-gray-800 mx-auto rounded-full mb-4" />
        </div>
        
        <div ref={projectsGridRef} className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
          {projects.map((project, index) => {
            const IconComponent = project.icon;
            const gradientMap: { [key: string]: string } = {
              blue: 'from-gray-600 to-gray-800',
              green: 'from-gray-400 to-gray-600',
              purple: 'from-gray-700 to-gray-900',
              orange: 'from-gray-500 to-gray-700'
            };
            
            return (
              <Card 
                key={project.id} 
                className="group border-0 bg-white/80 dark:bg-white/5 backdrop-blur-xl rounded-2xl shadow-lg dark:shadow-xl dark:shadow-black/10 ring-1 ring-gray-200/50 dark:ring-white/10 hover:bg-white dark:hover:bg-white/10 hover:ring-gray-300 dark:hover:ring-white/20 transition-all duration-500 hover:scale-[1.02] hover:shadow-xl dark:hover:shadow-2xl dark:hover:shadow-blue-500/10"
                style={{ animationDelay: `${index * 200}ms` }}
              >
                <CardHeader className="pb-4 md:pb-6">
                  <CardTitle className="flex items-center gap-3 text-xl md:text-2xl font-light text-gray-900 dark:text-white group-hover:text-gray-700 dark:group-hover:text-blue-300 transition-colors">
                    <div className={`w-12 h-12 md:w-14 md:h-14 rounded-xl bg-gradient-to-r ${gradientMap[project.color]} flex items-center justify-center shadow-lg shadow-gray-500/25`}>
                      <IconComponent className="w-6 h-6 md:w-7 md:h-7 text-white" />
                    </div>
                    {project.title}
                  </CardTitle>
                  <CardDescription className="text-base md:text-lg font-light text-gray-700 dark:text-blue-200/80 leading-relaxed md:pl-16">
                    {project.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-2 md:space-y-3">
                    {project.items.map((item, itemIndex) => {
                      const handleClick = () => {
                        if (item.isClickable && item.url) {
                          window.open(item.url, '_blank', 'noopener,noreferrer');
                        }
                      };

                      return (
                        <div
                          key={itemIndex}
                          className={`flex items-center gap-3 p-3 md:p-4 rounded-xl backdrop-blur-sm border transition-all duration-300 group/item ${
                            item.isClickable
                              ? 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200/80 dark:border-blue-400/20 hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-800/30 dark:hover:to-indigo-800/30 hover:border-blue-300 dark:hover:border-blue-300/30 cursor-pointer hover:scale-[1.02] hover:shadow-lg hover:shadow-blue-500/10 dark:hover:shadow-blue-500/20'
                              : 'bg-gray-100 dark:bg-white/5 border-gray-200/80 dark:border-white/10 hover:bg-gray-200/60 dark:hover:bg-white/10 hover:border-gray-300 dark:hover:border-white/20 cursor-default'
                          }`}
                          style={{ animationDelay: `${itemIndex * 100}ms` }}
                          onClick={handleClick}
                          title={item.isClickable ? (item.url?.includes('marketplace.visualstudio.com') ? 'Click to open in VS Code Marketplace' : 'Click to open in Chrome Web Store') : undefined}
                        >
                          <div className={`w-8 h-8 rounded-full bg-gradient-to-r ${gradientMap[project.color]} flex items-center justify-center flex-shrink-0 shadow-lg group-hover/item:scale-110 transition-transform ${
                            item.isClickable ? 'group-hover/item:shadow-xl group-hover/item:shadow-blue-500/25' : ''
                          }`}>
                            <Code className="w-4 h-4 text-white" />
                          </div>
                          <span className={`font-medium text-sm md:text-base transition-colors ${
                            item.isClickable
                              ? 'text-blue-800 dark:text-blue-200 group-hover/item:text-blue-900 dark:group-hover/item:text-blue-100'
                              : 'text-gray-800 dark:text-blue-100/90 group-hover/item:text-gray-900 dark:group-hover/item:text-white'
                          }`}>
                            {item.text}
                          </span>
                          <div className="ml-auto opacity-0 group-hover/item:opacity-100 transition-opacity">
                            {item.isClickable ? (
                              <ExternalLink className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                            ) : (
                              <div className="w-2 h-2 rounded-full bg-gray-400 animate-pulse" />
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </section>
    );
  }
);

ProjectsSection.displayName = 'ProjectsSection';

export default ProjectsSection;
