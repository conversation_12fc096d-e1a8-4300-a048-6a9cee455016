import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, LabelList } from 'recharts';
import { getCodebaseStats, getProjectTrivia, FileTypeStats } from '../../utils/codebaseAnalysis';

interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    payload: FileTypeStats;
    value: number;
    name: string;
  }>;
  label?: string;
}



const CustomTooltip: React.FC<CustomTooltipProps> = ({ active, payload }) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-yellow-50 border-2 border-gray-400 p-2 shadow-lg font-tahoma text-xs"
           style={{
             borderStyle: 'outset',
             borderWidth: '2px',
             backgroundColor: '#FFFBF0'
           }}>
        <div className="font-bold text-blue-900">{data.extension}</div>
        <div className="text-gray-700">Files: {data.count}</div>
        <div className="text-gray-700">Lines: {data.linesOfCode.toLocaleString()}</div>
        <div className="text-gray-700">Percentage: {data.percentage}%</div>
      </div>
    );
  }
  return null;
};

const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, extension }: {
  cx: number;
  cy: number;
  midAngle: number;
  innerRadius: number;
  outerRadius: number;
  percent: number;
  extension: string;
}) => {
  const RADIAN = Math.PI / 180;
  const radius = innerRadius + (outerRadius - innerRadius) * 1.4;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  if (percent < 0.05) return null; // Don't show labels for very small segments

  return (
    <text
      x={x}
      y={y}
      fill="#333"
      textAnchor={x > cx ? 'start' : 'end'}
      dominantBaseline="central"
      fontSize="11"
      fontFamily="Tahoma"
      fontWeight="bold"
    >
      {`${extension} (${(percent * 100).toFixed(1)}%)`}
    </text>
  );
};

const CodebasePieChart: React.FC = () => {
  const [selectedSegment, setSelectedSegment] = useState<string | null>(null);
  const stats = getCodebaseStats();
  const trivia = getProjectTrivia();

  const handlePieClick = (data: FileTypeStats) => {
    setSelectedSegment(selectedSegment === data.extension ? null : data.extension);
  };

  return (
    <div className="bg-white font-tahoma">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-100 to-blue-200 p-3 border-b-2 border-gray-300">
        <h3 className="font-bold text-blue-900 text-sm flex items-center">
          Codebase Composition
        </h3>
        <p className="text-xs text-gray-700 mt-1">
          Portfolio project file statistics and technical overview
        </p>
      </div>

      <div className="p-4 space-y-6">
        {/* Pie Chart Section */}
        <div className="bg-gray-50 border-2 border-gray-300 p-4"
             style={{
               borderStyle: 'inset',
               backgroundColor: '#F0F0F0'
             }}>
          <div className="h-64 mb-4">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={stats.fileTypes}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  innerRadius={30}
                  paddingAngle={2}
                  dataKey="percentage"
                  onClick={handlePieClick}
                  className="cursor-pointer"
                  label={renderCustomLabel}
                  labelLine={false}
                >
                  {stats.fileTypes.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={entry.color}
                      stroke="#666"
                      strokeWidth={selectedSegment === entry.extension ? 3 : 1}
                      style={{
                        filter: selectedSegment === entry.extension ? 'brightness(1.1)' : 'none'
                      }}
                    />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>



        {/* Selected File Type Details */}
        {selectedSegment && selectedSegment !== '.json' && (
          <div className="bg-yellow-50 border-2 border-yellow-400 p-3"
               style={{ borderStyle: 'inset', backgroundColor: '#FFFACD' }}>
            <div className="text-xs font-bold text-yellow-900 mb-2">
              🔍 {selectedSegment} File Details
            </div>
            {(() => {
              const fileType = stats.fileTypes.find(ft => ft.extension === selectedSegment);
              if (!fileType) return null;
              return (
                <div className="text-xs space-y-1">
                  <div><strong>File Count:</strong> {fileType.count} files</div>
                  <div><strong>Lines of Code:</strong> {fileType.linesOfCode.toLocaleString()}</div>
                  <div><strong>Percentage:</strong> {fileType.percentage}% of codebase</div>
                  <div><strong>Average per file:</strong> {Math.round(fileType.linesOfCode / fileType.count)} lines</div>
                </div>
              );
            })()}
          </div>
        )}

        {/* Technical Trivia */}
        <div className="bg-purple-50 border-2 border-purple-300 p-3"
             style={{ borderStyle: 'outset', backgroundColor: '#F5F0FF' }}>
          <div className="text-xs font-bold text-purple-900 mb-2">Technical Highlights</div>
          <div className="space-y-1 text-xs">
            {trivia.slice(0, 4).map((fact, index) => (
              <div key={index} className="flex items-start space-x-1">
                <span className="text-purple-600">•</span>
                <span>{fact}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CodebasePieChart;
