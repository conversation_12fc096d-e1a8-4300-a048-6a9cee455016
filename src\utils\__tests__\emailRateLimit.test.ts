/**
 * Tests for email rate limiting functionality
 * These tests verify the session-based rate limiting works correctly
 */

import { 
  getEmailRateLimitStatus, 
  incrementEmailCount, 
  resetEmailCount,
  getRateLimitMessage,
  validateEmailRateLimit,
  EMAIL_RATE_LIMIT 
} from '../emailRateLimit';

// Mock sessionStorage for testing
const mockSessionStorage = (() => {
  let store: Record<string, string> = {};
  
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value;
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    }
  };
})();

// Replace global sessionStorage with mock
Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage
});

describe('Email Rate Limiting', () => {
  beforeEach(() => {
    // Clear sessionStorage before each test
    mockSessionStorage.clear();
  });

  describe('getEmailRateLimitStatus', () => {
    it('should return initial status with zero count', () => {
      const status = getEmailRateLimitStatus();
      
      expect(status.count).toBe(0);
      expect(status.remaining).toBe(EMAIL_RATE_LIMIT.MAX_EMAILS_PER_SESSION);
      expect(status.isLimitReached).toBe(false);
      expect(status.canSendEmail).toBe(true);
    });

    it('should return correct status when emails have been sent', () => {
      // Simulate 2 emails sent
      mockSessionStorage.setItem(EMAIL_RATE_LIMIT.SESSION_STORAGE_KEY, '2');
      
      const status = getEmailRateLimitStatus();
      
      expect(status.count).toBe(2);
      expect(status.remaining).toBe(1);
      expect(status.isLimitReached).toBe(false);
      expect(status.canSendEmail).toBe(true);
    });

    it('should return limit reached when at maximum', () => {
      // Simulate maximum emails sent
      mockSessionStorage.setItem(EMAIL_RATE_LIMIT.SESSION_STORAGE_KEY, '3');
      
      const status = getEmailRateLimitStatus();
      
      expect(status.count).toBe(3);
      expect(status.remaining).toBe(0);
      expect(status.isLimitReached).toBe(true);
      expect(status.canSendEmail).toBe(false);
    });
  });

  describe('incrementEmailCount', () => {
    it('should increment count from zero', () => {
      const newCount = incrementEmailCount();
      
      expect(newCount).toBe(1);
      expect(mockSessionStorage.getItem(EMAIL_RATE_LIMIT.SESSION_STORAGE_KEY)).toBe('1');
    });

    it('should increment existing count', () => {
      mockSessionStorage.setItem(EMAIL_RATE_LIMIT.SESSION_STORAGE_KEY, '1');
      
      const newCount = incrementEmailCount();
      
      expect(newCount).toBe(2);
      expect(mockSessionStorage.getItem(EMAIL_RATE_LIMIT.SESSION_STORAGE_KEY)).toBe('2');
    });

    it('should store reset timestamp', () => {
      incrementEmailCount();
      
      const resetTime = mockSessionStorage.getItem(EMAIL_RATE_LIMIT.RESET_STORAGE_KEY);
      expect(resetTime).toBeTruthy();
      expect(Number(resetTime)).toBeGreaterThan(0);
    });
  });

  describe('resetEmailCount', () => {
    it('should clear all rate limit data', () => {
      // Set some data first
      mockSessionStorage.setItem(EMAIL_RATE_LIMIT.SESSION_STORAGE_KEY, '2');
      mockSessionStorage.setItem(EMAIL_RATE_LIMIT.RESET_STORAGE_KEY, '123456789');
      
      resetEmailCount();
      
      expect(mockSessionStorage.getItem(EMAIL_RATE_LIMIT.SESSION_STORAGE_KEY)).toBeNull();
      expect(mockSessionStorage.getItem(EMAIL_RATE_LIMIT.RESET_STORAGE_KEY)).toBeNull();
    });
  });

  describe('getRateLimitMessage', () => {
    it('should return appropriate message for zero emails', () => {
      const status = { count: 0, remaining: 3, isLimitReached: false, canSendEmail: true };
      const message = getRateLimitMessage(status);
      
      expect(message).toContain('You can send up to 3 emails');
    });

    it('should return progress message for partial usage', () => {
      const status = { count: 2, remaining: 1, isLimitReached: false, canSendEmail: true };
      const message = getRateLimitMessage(status);
      
      expect(message).toContain('Emails sent this session: 2/3');
    });

    it('should return limit reached message', () => {
      const status = { count: 3, remaining: 0, isLimitReached: true, canSendEmail: false };
      const message = getRateLimitMessage(status);
      
      expect(message).toContain('Email limit reached');
      expect(message).toContain('refresh your browser');
    });
  });

  describe('validateEmailRateLimit', () => {
    it('should allow sending when under limit', () => {
      const result = validateEmailRateLimit();
      
      expect(result.canSend).toBe(true);
      expect(result.message).toBeUndefined();
    });

    it('should prevent sending when at limit', () => {
      mockSessionStorage.setItem(EMAIL_RATE_LIMIT.SESSION_STORAGE_KEY, '3');
      
      const result = validateEmailRateLimit();
      
      expect(result.canSend).toBe(false);
      expect(result.message).toContain('Email limit reached');
    });
  });
});
