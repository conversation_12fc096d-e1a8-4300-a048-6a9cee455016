# Codebase Consolidation & Refactoring Todo

This document outlines the comprehensive plan to eliminate redundant component implementations and reduce code duplication while preserving the existing Windows XP aesthetic and functionality.

## ⚡ Phase 2: Theme Unification
*Critical consolidation of theme management systems*

### 2.2 Build Reusable Theme Toggle Component
- [x] **Create configurable theme toggle**
  - Create `src/shared/components/ThemeToggle.tsx`
  - Support variants: `"modern-dark-light"`, `"xp-modern"`
  - Support positions: `"fixed"`, `"inline"`
  - **Lines saved**: ~50

- [x] **Replace existing theme switchers**
  - Replace `src/modern/src/components/ThemeSwitcher.tsx`
  - Replace theme switching in `src/modern/src/components/Navigation.tsx`
  - Replace theme switching in `src/components/DialogManager.tsx`
  - **Implementation note**: Maintain existing styling and positioning

## 📋 Phase 3: Structural Improvements
*Long-term architectural improvements*

### 3.1 Create Generic Folder Window Component
- [x] **Analyze folder window patterns**
  - Document common patterns in `MyComputerContent`, `MyDocumentsContent`, `MyPicturesContent`
  - Identify reusable sidebar configurations

- [x] **Design generic folder interface**
  ```typescript
  interface FolderWindowProps {
    title: string;
    icon: React.ReactNode;
    items: FolderItem[];
    sidebarConfig?: SidebarConfig;
    onItemClick: (item: FolderItem) => void;
    onOpenWindow?: (id: string, name: string) => void;
  }
  ```

- [x] **Implement generic FolderWindow component**
  - Create `src/shared/components/FolderWindow.tsx`
  - Integrate with existing `WindowsFolderHeader`
  - **Lines saved**: ~100
  - **Gotcha**: Ensure Windows XP styling consistency

- [x] **Migrate existing folder windows**
  - Refactor `MyComputerContent` to use generic component
  - Refactor `MyDocumentsContent` to use generic component
  - Refactor `MyPicturesContent` to use generic component
  - **Implementation note**: Preserve all existing functionality and styling

### 3.2 Unify App Structure
- [x] **Create shared app wrapper**
  - Create `src/shared/components/UnifiedApp.tsx`
  - Implement theme-aware rendering logic
  - **Lines saved**: ~30

- [x] **Consolidate provider setup**
  - Merge provider logic from both `App.tsx` files
  - Create single entry point with theme routing
  - **Gotcha**: Ensure proper provider nesting and context availability

- [x] **Update main entry points**
  - Simplify `src/main.tsx`
  - Simplify `src/modern/src/main.tsx`
  - **Implementation note**: Maintain existing routing behavior

### 3.3 Optimize Component Imports
- [x] **Audit unused UI components**
  - Identify shadcn/ui components only used in one theme
  - **Files to check**: All files in `src/components/ui/`

- [x] **Remove unused components**
  - Delete components not used in either theme
  - **Verification**: Run build to ensure no missing dependencies

- [x] **Optimize bundle splitting**
  - Update `vite.config.ts` manual chunks configuration
  - Separate theme-specific code into different chunks

---
## 📝 Notes & Gotchas

### Critical Considerations
- **Preserve Windows XP aesthetic**: All changes must maintain authentic Windows XP styling
- **Theme switching**: Ensure seamless transitions between XP and modern themes
- **Mobile compatibility**: Maintain existing mobile navigation behavior
- **SEO preservation**: Don't break existing SEO optimizations

### Potential Issues
- **Import path updates**: May require extensive find/replace operations
- **Context migration**: Ensure all theme consumers are updated simultaneously
- **Bundle optimization**: Watch for circular dependencies in shared components
- **TypeScript**: Update type definitions for unified interfaces

### Performance Considerations
- **Lazy loading**: Consider lazy loading theme-specific components
- **Code splitting**: Ensure shared components don't bloat both theme bundles
- **Tree shaking**: Verify unused code is properly eliminated