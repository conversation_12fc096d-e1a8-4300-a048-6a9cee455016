/* Modern App specific styles */

/* App container styles */
.modern-app {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* Background patterns and effects */
.modern-bg-pattern {
  background-image: 
    linear-gradient(rgba(107, 114, 128, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(107, 114, 128, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Floating orb animations */
.floating-orb {
  animation: float 6s ease-in-out infinite;
}

.floating-orb:nth-child(2) {
  animation-delay: -2s;
}

.floating-orb:nth-child(3) {
  animation-delay: -4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-20px) scale(1.05);
  }
}

/* Interactive mouse glow effect */
.mouse-glow {
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Card hover effects */
.modern-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-card:hover {
  transform: translateY(-4px) scale(1.02);
}

/* Navigation styles */
.modern-nav {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Button styles */
.modern-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-button:hover {
  transform: translateY(-1px);
}

.modern-button:active {
  transform: translateY(0);
}

/* Text selection */
::selection {
  background-color: rgba(65, 105, 225, 0.3);
  color: inherit;
}

/* Focus styles */
.modern-focus:focus {
  outline: 2px solid #4169E1;
  outline-offset: 2px;
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Dark mode specific adjustments */
.dark .loading-shimmer {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.1) 50%, 
    rgba(255, 255, 255, 0) 100%);
}

/* Print styles */
@media print {
  .modern-app {
    background: white !important;
  }
  
  .floating-orb,
  .mouse-glow,
  .modern-nav {
    display: none !important;
  }
}
