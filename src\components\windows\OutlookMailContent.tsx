

import React, { useState, useEffect } from 'react';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';
import { Button } from '../../components/ui/button';
import { Mail, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { sendContactEmail, type ContactFormData } from '../../services/emailService';
import {
  getEmailRateLimitStatus,
  incrementEmailCount,
  getRateLimitMessage,
  validateEmailRateLimit,
  EMAIL_RATE_LIMIT
} from '../../utils/emailRateLimit';

interface OutlookMailContentProps {
  onClose?: () => void;
}

const OutlookMailContent: React.FC<OutlookMailContentProps> = ({ onClose }) => {
  const [name, setName] = useState('');
  const [fromEmail, setFromEmail] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [rateLimitStatus, setRateLimitStatus] = useState(getEmailRateLimitStatus());
  const toEmail = '<EMAIL>';

  // Load rate limit status on component mount
  useEffect(() => {
    setRateLimitStatus(getEmailRateLimitStatus());
  }, []);

  const handleSend = async () => {
    // Check rate limiting
    const rateLimitCheck = validateEmailRateLimit();
    if (!rateLimitCheck.canSend) {
      toast.error(rateLimitCheck.message!, {
        duration: 4000,
      });
      return;
    }

    setIsLoading(true);

    try {
      // Prepare form data
      const formData: ContactFormData = {
        name: name.trim(),
        fromEmail: fromEmail.trim(),
        subject: subject.trim(),
        message: message.trim(),
      };

      // Send email via service
      const result = await sendContactEmail(formData);

      if (result.success) {
        // Update rate limit status
        incrementEmailCount();
        setRateLimitStatus(getEmailRateLimitStatus());

        toast.success('Email sent successfully!', {
          duration: 3000,
        });

        // Clear form
        setName('');
        setFromEmail('');
        setSubject('');
        setMessage('');

        // Close dialog after a short delay
        setTimeout(() => {
          if (onClose) {
            onClose();
          }
        }, 1500);
      } else {
        toast.error(result.error || 'Failed to send email. Please try again later.', {
          duration: 4000,
        });
      }
    } catch (error) {
      console.error('Failed to send email:', error);
      toast.error('An unexpected error occurred. Please try again later.', {
        duration: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full bg-gray-100 border-t border-l border-r border-b border-gray-400 shadow-inner">
      <div className="flex items-center p-1 bg-gradient-to-r from-blue-600 to-blue-400 text-white text-sm font-bold">
        <Mail size={16} className="mr-1" />
        New Message
      </div>
      <div className="p-2 flex-grow overflow-auto">
        <div className="flex items-center mb-2">
          <label className="w-16 text-sm font-semibold text-gray-700">To:</label>
          <Input
            type="email"
            value={toEmail}
            readOnly
            className="flex-1 bg-gray-200 border border-gray-300 rounded-sm px-2 py-1 text-sm"
          />
        </div>
        <div className="flex items-center mb-2">
          <label className="w-16 text-sm font-semibold text-gray-700">Name:</label>
          <Input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Your full name"
            disabled={isLoading}
            className="flex-1 bg-white border border-gray-300 rounded-sm px-2 py-1 text-sm focus:outline-none focus:border-blue-500 disabled:bg-gray-100"
          />
        </div>
        <div className="flex items-center mb-2">
          <label className="w-16 text-sm font-semibold text-gray-700">From:</label>
          <Input
            type="email"
            value={fromEmail}
            onChange={(e) => setFromEmail(e.target.value)}
            placeholder="<EMAIL>"
            disabled={isLoading}
            className="flex-1 bg-white border border-gray-300 rounded-sm px-2 py-1 text-sm focus:outline-none focus:border-blue-500 disabled:bg-gray-100"
          />
        </div>
        <div className="flex items-center mb-2">
          <label className="w-16 text-sm font-semibold text-gray-700">Subject:</label>
          <Input
            type="text"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
            placeholder="Email subject"
            disabled={isLoading}
            className="flex-1 bg-white border border-gray-300 rounded-sm px-2 py-1 text-sm focus:outline-none focus:border-blue-500 disabled:bg-gray-100"
          />
        </div>
        <div className="flex-grow mb-2">
          <Textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Type your message here..."
            disabled={isLoading}
            className="w-full h-full min-h-[150px] bg-white border border-gray-300 rounded-sm p-2 text-sm resize-none focus:outline-none focus:border-blue-500 disabled:bg-gray-100"
          />
        </div>

        {/* Rate limiting indicator */}
        {rateLimitStatus.count > 0 && (
          <div className="mb-2 p-2 bg-blue-50 border border-blue-200 rounded-sm">
            <div className="flex items-center text-xs text-blue-700">
              <AlertCircle size={12} className="mr-1" />
              <span>{getRateLimitMessage(rateLimitStatus)}</span>
            </div>
          </div>
        )}
      </div>
      <div className="p-2 border-t border-gray-300 flex justify-end">
        <Button
          onClick={handleSend}
          disabled={isLoading || rateLimitStatus.isLimitReached}
          className="px-4 py-1 bg-gradient-to-b from-blue-600 to-blue-500 text-white text-sm font-semibold rounded-sm shadow-md hover:from-blue-700 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 disabled:from-gray-400 disabled:to-gray-300 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Sending...' : 'Send'}
        </Button>
      </div>
    </div>
  );
};

export default OutlookMailContent;
