import { useState, useCallback, useRef, useEffect } from 'react';

export type DialogType =
  | 'welcome'
  | 'contact'
  | 'notification'
  | 'shutdown'
  | 'error'
  | 'confirmation'
  | 'search'
  | string; // Allow custom dialog types

export interface DialogState {
  id: string;
  type: DialogType;
  isOpen: boolean;
  data?: any;
  zIndex: number;
}

export interface DialogManagerOptions {
  preventMultipleInstances?: boolean;
  maxDialogs?: number;
  baseZIndex?: number;
}

/**
 * Global dialog manager hook that prevents multiple instances of the same dialog type
 * and avoids React rendering conflicts by batching state updates
 */
export const useDialogManager = (options: DialogManagerOptions = {}) => {
  const {
    preventMultipleInstances = true,
    maxDialogs = 5,
    baseZIndex = 9000
  } = options;

  const [dialogs, setDialogs] = useState<DialogState[]>([]);
  const [maxZIndex, setMaxZIndex] = useState(baseZIndex);
  const pendingUpdatesRef = useRef<(() => void)[]>([]);
  const isUpdatingRef = useRef(false);

  // Batch state updates to prevent React rendering conflicts
  const batchUpdates = useCallback((updateFn: () => void) => {
    if (isUpdatingRef.current) {
      // If we're already updating, queue this update for the next tick
      pendingUpdatesRef.current.push(updateFn);
      return;
    }

    isUpdatingRef.current = true;
    updateFn();
    
    // Process any pending updates in the next tick
    setTimeout(() => {
      const pendingUpdates = [...pendingUpdatesRef.current];
      pendingUpdatesRef.current = [];
      isUpdatingRef.current = false;
      
      if (pendingUpdates.length > 0) {
        pendingUpdates.forEach(update => update());
      }
    }, 0);
  }, []);

  // Check if a dialog of the specified type is already open
  const isDialogOpen = useCallback((type: DialogType): boolean => {
    return dialogs.some(dialog => dialog.type === type && dialog.isOpen);
  }, [dialogs]);

  // Get dialog by type
  const getDialog = useCallback((type: DialogType): DialogState | undefined => {
    return dialogs.find(dialog => dialog.type === type && dialog.isOpen);
  }, [dialogs]);

  // Open a dialog with single-instance protection
  const openDialog = useCallback((
    type: DialogType, 
    data?: any, 
    options?: { allowMultiple?: boolean; id?: string }
  ): string | null => {
    const { allowMultiple = false, id } = options || {};
    
    // Check if we should prevent multiple instances
    if (preventMultipleInstances && !allowMultiple && isDialogOpen(type)) {
      // Bring existing dialog to front instead of opening a new one
      batchUpdates(() => {
        setDialogs(prev => prev.map(dialog => 
          dialog.type === type && dialog.isOpen
            ? { ...dialog, zIndex: maxZIndex + 1 }
            : dialog
        ));
        setMaxZIndex(prev => prev + 1);
      });
      return getDialog(type)?.id || null;
    }

    // Check max dialogs limit
    const openDialogCount = dialogs.filter(d => d.isOpen).length;
    if (openDialogCount >= maxDialogs) {
      console.warn(`Maximum number of dialogs (${maxDialogs}) reached. Cannot open new dialog of type: ${type}`);
      return null;
    }

    const dialogId = id || `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newZIndex = maxZIndex + 1;

    const newDialog: DialogState = {
      id: dialogId,
      type,
      isOpen: true,
      data,
      zIndex: newZIndex
    };

    batchUpdates(() => {
      setDialogs(prev => [...prev, newDialog]);
      setMaxZIndex(newZIndex);
    });

    return dialogId;
  }, [dialogs, maxZIndex, preventMultipleInstances, maxDialogs, isDialogOpen, getDialog, batchUpdates]);

  // Close a dialog by ID or type
  const closeDialog = useCallback((identifier: string | DialogType) => {
    batchUpdates(() => {
      setDialogs(prev => prev.map(dialog => {
        const shouldClose = dialog.id === identifier || dialog.type === identifier;
        return shouldClose ? { ...dialog, isOpen: false } : dialog;
      }));
    });

    // Clean up closed dialogs after animation
    setTimeout(() => {
      batchUpdates(() => {
        setDialogs(prev => prev.filter(dialog => {
          const shouldRemove = (dialog.id === identifier || dialog.type === identifier) && !dialog.isOpen;
          return !shouldRemove;
        }));
      });
    }, 300); // Allow time for close animations
  }, [batchUpdates]);

  // Close all dialogs
  const closeAllDialogs = useCallback(() => {
    batchUpdates(() => {
      setDialogs(prev => prev.map(dialog => ({ ...dialog, isOpen: false })));
    });

    // Clean up after animations
    setTimeout(() => {
      batchUpdates(() => {
        setDialogs([]);
      });
    }, 300);
  }, [batchUpdates]);

  // Bring dialog to front
  const bringToFront = useCallback((identifier: string | DialogType) => {
    batchUpdates(() => {
      setDialogs(prev => prev.map(dialog => {
        const shouldBringToFront = dialog.id === identifier || dialog.type === identifier;
        return shouldBringToFront ? { ...dialog, zIndex: maxZIndex + 1 } : dialog;
      }));
      setMaxZIndex(prev => prev + 1);
    });
  }, [maxZIndex, batchUpdates]);

  // Get all open dialogs
  const getOpenDialogs = useCallback((): DialogState[] => {
    return dialogs.filter(dialog => dialog.isOpen);
  }, [dialogs]);

  // Get dialog count by type
  const getDialogCount = useCallback((type?: DialogType): number => {
    if (type) {
      return dialogs.filter(dialog => dialog.type === type && dialog.isOpen).length;
    }
    return dialogs.filter(dialog => dialog.isOpen).length;
  }, [dialogs]);

  // Clean up effect
  useEffect(() => {
    return () => {
      pendingUpdatesRef.current = [];
      isUpdatingRef.current = false;
    };
  }, []);

  return {
    dialogs: getOpenDialogs(),
    openDialog,
    closeDialog,
    closeAllDialogs,
    bringToFront,
    isDialogOpen,
    getDialog,
    getDialogCount,
    maxZIndex
  };
};

// Global dialog manager instance for use across the application
let globalDialogManager: ReturnType<typeof useDialogManager> | null = null;

export const getGlobalDialogManager = () => globalDialogManager;
export const setGlobalDialogManager = (manager: ReturnType<typeof useDialogManager>) => {
  globalDialogManager = manager;
};

/**
 * Utility function to safely open a dialog with single-instance protection
 * Can be used outside of React components
 */
export const safeOpenDialog = (
  type: DialogType,
  data?: any,
  options?: { allowMultiple?: boolean; id?: string }
): string | null => {
  const manager = getGlobalDialogManager();
  if (!manager) {
    console.warn('Dialog manager not initialized. Make sure to call setGlobalDialogManager in your app.');
    return null;
  }
  return manager.openDialog(type, data, options);
};

/**
 * Utility function to safely close a dialog
 * Can be used outside of React components
 */
export const safeCloseDialog = (identifier: string | DialogType): void => {
  const manager = getGlobalDialogManager();
  if (!manager) {
    console.warn('Dialog manager not initialized. Make sure to call setGlobalDialogManager in your app.');
    return;
  }
  manager.closeDialog(identifier);
};

/**
 * Utility function to check if a dialog is open
 * Can be used outside of React components
 */
export const isDialogCurrentlyOpen = (type: DialogType): boolean => {
  const manager = getGlobalDialogManager();
  if (!manager) {
    return false;
  }
  return manager.isDialogOpen(type);
};
