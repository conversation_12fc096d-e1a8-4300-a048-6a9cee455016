# Dialog Management System

This document describes the global dialog management system that prevents multiple instances of dialog windows and avoids React rendering conflicts in the Windows XP-style desktop application.

## Overview

The dialog management system provides:
- **Single-instance protection**: Prevents multiple dialogs of the same type from opening
- **React rendering conflict prevention**: Batches state updates to avoid "Cannot update a component while rendering" errors
- **Global state management**: Centralized dialog state accessible throughout the application
- **Windows XP styling**: Authentic Windows XP dialog appearance
- **Z-index management**: Proper layering of multiple dialogs

## Core Components

### 1. `useDialogManager` Hook
The main hook that manages dialog state and provides dialog operations.

```typescript
const dialogManager = useDialogManager({
  preventMultipleInstances: true, // Default: true
  maxDialogs: 5,                  // Default: 5
  baseZIndex: 9000               // Default: 9000
});
```

### 2. `DialogProvider` Context
Provides dialog management throughout the React component tree.

```tsx
<DialogProvider>
  <YourApp />
</DialogProvider>
```

### 3. `DialogManager` Component
Renders all active dialogs with Windows XP styling.

```tsx
<DialogManager />
```

## Usage Examples

### Basic Dialog Operations

```typescript
import { useDialog } from '../contexts/DialogContext';

const MyComponent = () => {
  const { openDialog, closeDialog, isDialogOpen } = useDialog();
  
  const handleOpenDialog = () => {
    if (!isDialogOpen('contact')) {
      openDialog('contact', { message: 'Hello!' });
    }
  };
  
  return <button onClick={handleOpenDialog}>Open Contact</button>;
};
```

### Using Utility Functions

```typescript
import { 
  showMessageBox, 
  showConfirmBox, 
  openNotificationDialog 
} from '../utils/dialogUtils';

// Simple message box
showMessageBox('Operation completed!', 'Success', 'info');

// Confirmation dialog
showConfirmBox(
  'Are you sure?',
  () => console.log('Confirmed'),
  'Confirm Action'
);

// Notification with details
openNotificationDialog(
  'Update Available',
  'A new version is available for download.',
  '📢',
  'Click here to learn more about the update.'
);
```

### Safe Dialog Operations Outside React

```typescript
import { safeOpenDialog, safeCloseDialog } from '../hooks/useDialogManager';

// Can be called from anywhere in the application
const handleGlobalEvent = () => {
  safeOpenDialog('notification', {
    title: 'Global Event',
    message: 'Something happened!'
  });
};
```

## Dialog Types

### Built-in Dialog Types

- `welcome`: Welcome dialog with quick actions
- `notification`: Information notifications
- `confirmation`: Yes/No confirmation dialogs
- `error`: Error message dialogs
- `contact`: Contact information dialogs

### Custom Dialog Types

You can create custom dialog types by passing any string as the dialog type:

```typescript
openDialog('my-custom-dialog', {
  title: 'Custom Dialog',
  content: <MyCustomComponent />
});
```

## Preventing Multiple Instances

The system automatically prevents multiple instances of the same dialog type:

```typescript
// First call opens the dialog
openDialog('contact', { message: 'First' });

// Second call brings existing dialog to front (doesn't create new one)
openDialog('contact', { message: 'Second' });
```

To allow multiple instances:

```typescript
openDialog('notification', data, { allowMultiple: true });
```

## React Rendering Conflict Prevention

The system uses batched state updates to prevent React rendering conflicts:

```typescript
// These rapid calls are safely batched
openDialog('dialog1', data1);
openDialog('dialog2', data2);
closeDialog('dialog1');
```

## Integration with Existing Windows

The dialog system works alongside the existing window management system:

```typescript
const Desktop = () => {
  const { openWindow } = useWindowManager();
  const { openDialog } = useDialog();
  
  const handleAction = () => {
    // Open a window
    openWindow('contact', 'Contact Info');
    
    // Show a notification dialog
    openDialog('notification', {
      title: 'Window Opened',
      message: 'Contact window has been opened.'
    });
  };
};
```

## Best Practices

### 1. Use Utility Functions
Prefer utility functions over direct dialog manager calls:

```typescript
// Good
showMessageBox('Success!', 'Operation Complete');

// Less preferred
openDialog('notification', { title: 'Operation Complete', message: 'Success!' });
```

### 2. Check Dialog State Before Opening
Always check if a dialog is already open when appropriate:

```typescript
if (!isDialogOpen('contact')) {
  openDialog('contact', data);
}
```

### 3. Handle Cleanup
The system automatically handles cleanup, but you can manually close dialogs:

```typescript
useEffect(() => {
  return () => {
    closeDialog('my-dialog'); // Cleanup on unmount
  };
}, []);
```

### 4. Use Specific Dialog IDs for Multiple Instances
When you need multiple instances, use specific IDs:

```typescript
openDialog('notification', data, { 
  allowMultiple: true, 
  id: 'notification-1' 
});
```

## Error Handling

The system gracefully handles errors:

```typescript
// Safe operations that won't crash the app
const result = safeOpenDialog('dialog', data);
if (!result) {
  console.log('Dialog could not be opened');
}
```

## Performance Considerations

- Dialog state is optimized with React.memo and useCallback
- Batched updates prevent unnecessary re-renders
- Automatic cleanup prevents memory leaks
- Z-index management is efficient

## Troubleshooting

### Dialog Not Opening
1. Check if DialogProvider is wrapping your component
2. Verify dialog type is not already open (if single-instance is enabled)
3. Check console for warnings about max dialog limit

### React Rendering Errors
The batched update system should prevent these, but if you encounter them:
1. Ensure you're using the provided utility functions
2. Avoid calling dialog operations during render cycles
3. Use useEffect for dialog operations triggered by state changes

### Styling Issues
1. Ensure DialogManager is rendered in your component tree
2. Check z-index conflicts with other elements
3. Verify Windows XP CSS classes are available
