import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { seoAssetsPlugin } from "./vite-plugins/seo-assets";

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    host: "::",
    port: 8080,
    proxy: {
      '/api': {
        target: 'http://localhost:8787',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  plugins: [react(), seoAssetsPlugin()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "@modern": path.resolve(__dirname, "./src/modern/src"),
      "@shared": path.resolve(__dirname, "./src/shared"),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'esbuild',
    chunkSizeWarningLimit: 1000,
    emptyOutDir: true,
    cssCodeSplit: true,
    assetsInlineLimit: 4096,
    rollupOptions: {
      output: {
        entryFileNames: 'static/js/[name].[hash].js',
        chunkFileNames: 'static/js/[name].[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || [];
          const ext = info[info.length - 1];
          
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `static/media/[name].[hash][extname]`;
          }
          if (ext === 'css') {
            return `static/css/[name].[hash][extname]`;
          }
          return `static/[ext]/[name].[hash][extname]`;
        },
        manualChunks: (id) => {
          // All node_modules go into vendor chunk to reduce chunk count
          if (id.includes('node_modules')) {
            // Keep chart libraries separate as they're large and not always needed
            if (id.includes('recharts') || id.includes('d3-')) {
              return 'charts';
            }
            return 'vendor';
          }

          // Application code chunks
          if (id.includes('/src/modern/')) {
            return 'modern-theme';
          }

          if (id.includes('/src/components/') && !id.includes('/src/modern/') && !id.includes('/src/shared/')) {
            return 'xp-theme';
          }

          if (id.includes('/src/shared/')) {
            return 'shared';
          }

          // Default chunk for other application code
          return 'app';
        },
      },
    },
  },
});
