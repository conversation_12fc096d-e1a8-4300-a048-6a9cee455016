/**
 * Mobile Navigation Test Utilities
 * 
 * This file contains utility functions for testing mobile navigation functionality
 * Can be used in browser console or automated tests
 */

// Test if mobile navigation is working
export const testMobileNavigation = () => {
  console.log('🧪 Testing Mobile Navigation...');
  
  // Check if we're on mobile
  const isMobile = window.innerWidth < 768;
  console.log(`📱 Mobile detected: ${isMobile}`);
  
  if (!isMobile) {
    console.log('⚠️ Not on mobile device - mobile navigation may not be active');
  }
  
  // Check if navigation context is available
  const hasNavigationContext = document.querySelector('[data-mobile-navigation]') !== null;
  console.log(`🧭 Navigation context available: ${hasNavigationContext}`);
  
  // Test browser history
  const initialHistoryLength = window.history.length;
  console.log(`📚 Initial history length: ${initialHistoryLength}`);
  
  return {
    isMobile,
    hasNavigationContext,
    initialHistoryLength
  };
};

// Simulate back button press
export const simulateBackButton = () => {
  console.log('⬅️ Simulating back button press...');
  window.history.back();
};

// Test navigation state
export const logNavigationState = () => {
  console.log('📊 Current Navigation State:');
  console.log(`- URL: ${window.location.href}`);
  console.log(`- History length: ${window.history.length}`);
  console.log(`- History state:`, window.history.state);
  
  // Try to access navigation context (if available)
  try {
    const event = new CustomEvent('getNavigationState');
    window.dispatchEvent(event);
  } catch (error) {
    console.log('- Navigation context not accessible from console');
  }
};

// Test sequence for manual testing
export const runMobileNavigationTestSequence = async () => {
  console.log('🚀 Starting Mobile Navigation Test Sequence...');
  
  const initialState = testMobileNavigation();
  
  if (!initialState.isMobile) {
    console.log('❌ Test requires mobile device or mobile emulation');
    return;
  }
  
  console.log('\n📋 Manual Test Steps:');
  console.log('1. Open a window (click on desktop icon)');
  console.log('2. Press device back button or call simulateBackButton()');
  console.log('3. Verify you return to desktop');
  console.log('4. Open another window');
  console.log('5. Open a nested window/dialog');
  console.log('6. Press back button twice');
  console.log('7. Verify you return to desktop');
  console.log('8. From desktop, press back button');
  console.log('9. Verify app closes or shows exit behavior');
  
  console.log('\n🔧 Available test functions:');
  console.log('- simulateBackButton() - Simulates back button press');
  console.log('- logNavigationState() - Shows current navigation state');
  console.log('- testMobileNavigation() - Checks if mobile navigation is working');
  
  return initialState;
};

// Add test functions to window for console access
if (typeof window !== 'undefined') {
  (window as any).mobileNavTest = {
    test: testMobileNavigation,
    simulateBack: simulateBackButton,
    logState: logNavigationState,
    runSequence: runMobileNavigationTestSequence
  };
  
  console.log('🧪 Mobile Navigation Test utilities loaded!');
  console.log('Use window.mobileNavTest.runSequence() to start testing');
}

// Export for use in components
export default {
  testMobileNavigation,
  simulateBackButton,
  logNavigationState,
  runMobileNavigationTestSequence
};
