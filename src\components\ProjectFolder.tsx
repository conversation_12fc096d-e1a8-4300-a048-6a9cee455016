import React from 'react';

interface ProjectFolderProps {
  name: string;
  description: string;
  icon: string;
  onClick: () => void;
}

const ProjectFolder: React.FC<ProjectFolderProps> = ({ name, description, icon, onClick }) => {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onClick();
  };

  return (
    <div
      className="flex flex-col items-center p-3 sm:p-4 cursor-pointer group transition-all duration-300 hover:bg-blue-50 rounded-lg border border-transparent hover:border-blue-200 hover:shadow-lg active:scale-95"
      onClick={handleClick}
      onDoubleClick={handleClick}
    >
      {/* Folder Icon */}
      <div className="relative mb-3 group-hover:scale-110 transition-transform duration-300 ease-out">
        <svg
          width="72"
          height="72"
          viewBox="0 0 64 64"
          className="drop-shadow-lg group-hover:drop-shadow-xl transition-all duration-300"
        >
          {/* Folder back */}
          <path
            d="M8 16 L8 52 C8 54 10 56 12 56 L52 56 C54 56 56 54 56 52 L56 20 C56 18 54 16 52 16 L32 16 L28 12 L12 12 C10 12 8 14 8 16 Z"
            fill="#FFD93D"
            stroke="#E6C200"
            strokeWidth="1.5"
            className="group-hover:fill-yellow-300 transition-colors duration-300"
          />
          {/* Folder front */}
          <path
            d="M8 20 L8 52 C8 54 10 56 12 56 L52 56 C54 56 56 54 56 52 L56 24 C56 22 54 20 52 20 L12 20 C10 20 8 22 8 20 Z"
            fill="#FFED4E"
            stroke="#E6C200"
            strokeWidth="1.5"
            className="group-hover:fill-yellow-200 transition-colors duration-300"
          />
          {/* Folder tab */}
          <path
            d="M12 12 L28 12 L32 16 L52 16 C54 16 56 18 56 20 L8 20 C8 18 10 16 12 16 L12 12 Z"
            fill="#FFF59D"
            stroke="#E6C200"
            strokeWidth="1.5"
            className="group-hover:fill-yellow-100 transition-colors duration-300"
          />
        </svg>

        {/* Project Icon Overlay */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-2xl sm:text-3xl group-hover:scale-110 transition-transform duration-300">
          {icon}
        </div>
      </div>

      {/* Project Name */}
      <div className="text-center max-w-[140px]">
        <h3 className="text-sm sm:text-base font-medium text-gray-800 mb-1 group-hover:text-blue-600 transition-colors duration-300 leading-tight">
          {name}
        </h3>
        <p className="text-xs text-gray-500 leading-tight group-hover:text-gray-600 transition-colors duration-300">
          {description}
        </p>
      </div>
    </div>
  );
};

export default ProjectFolder;
