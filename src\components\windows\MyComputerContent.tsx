
import FolderWindow from '@shared/components/FolderWindow';
import { SidebarConfig } from '@shared/types/FolderWindow';
import CodebasePieChart from './CodebasePieChart';
import { Computer } from 'lucide-react';
import { getTechnologyIcon } from '@shared/icons/TechnologyIcons';

interface MyComputerContentProps {
  onOpenWindow?: (id: string, name: string) => void;
}

const MyComputerContent = ({ onOpenWindow }: MyComputerContentProps) => {
  // Handler functions for sidebar navigation
  const handleAddRemovePrograms = () => {
    if (onOpenWindow) {
      onOpenWindow('projects', 'My Projects');
    }
  };

  const handleMyDocuments = () => {
    if (onOpenWindow) {
      onOpenWindow('my_documents', 'My Documents');
    }
  };

  const handleMyNetworkPlaces = () => {
    if (onOpenWindow) {
      onOpenWindow('contact', 'Contact Info');
    }
  };

  const handleRecycleBin = () => {
    if (onOpenWindow) {
      onOpenWindow('recyclebin', 'Recycle Bin');
    }
  };

  // Sidebar configuration
  const sidebarConfig: SidebarConfig = {
    sections: [
      {
        title: "System Tasks",
        buttons: [
          {
            id: "add-remove-programs",
            label: "Add or remove programs",
            onClick: handleAddRemovePrograms
          }
        ]
      },
      {
        title: "Other Places",
        buttons: [
          {
            id: "my-documents",
            label: "📁 My Documents",
            onClick: handleMyDocuments
          },
          {
            id: "my-network-places",
            label: "🌐 My Network Places",
            onClick: handleMyNetworkPlaces
          },
          {
            id: "recycle-bin",
            label: "🗑️ Recycle Bin",
            onClick: handleRecycleBin
          }
        ]
      }
    ],
    details: {
      title: "My Computer",
      description: "System folder containing portfolio application information and technology stack details."
    }
  };
  const techStack = [
    // Core Technologies
    { name: 'React', version: '18.3.1', description: 'JavaScript library for building dynamic user interfaces', ...getTechnologyIcon('React')! },
    { name: 'TypeScript', version: 'Latest', description: 'Typed superset of JavaScript for robust, scalable applications', ...getTechnologyIcon('TypeScript')! },
    { name: 'React Router', version: '6.26.2', description: 'Declarative routing for single-page React applications', ...getTechnologyIcon('React Router')! },

    // Styling & UI
    { name: 'Tailwind CSS', version: 'Latest', description: 'A utility-first CSS framework for rapid UI development', ...getTechnologyIcon('Tailwind CSS')! },
    { name: 'Shadcn/ui', version: 'Latest', description: 'Beautifully designed, accessible components built on Radix UI', ...getTechnologyIcon('Shadcn/ui')! },
    { name: 'Lucide React', version: '0.462.0', description: 'Beautiful & consistent icon toolkit for a clean UI', ...getTechnologyIcon('Lucide React')! },
    { name: 'Recharts', version: '2.12.7', description: 'A composable charting library for data visualization', ...getTechnologyIcon('Recharts')! },
    { name: 'Sonner', version: '1.5.0', description: 'An opinionated toast component for user notifications', ...getTechnologyIcon('Sonner')! },
    { name: 'React Resizable Panels', version: '2.1.3', description: 'Components for creating flexible, resizable panel layouts', ...getTechnologyIcon('React Resizable Panels')! },
    { name: 'React Icons', version: '5.5.0', description: 'Extensive library of popular icons for React projects', ...getTechnologyIcon('React Icons')! },

    // State Management & Forms
    { name: 'TanStack Query', version: '5.56.2', description: 'Powerful asynchronous state management and data-fetching', ...getTechnologyIcon('TanStack Query')! },
    { name: 'React Hook Form', version: '7.53.0', description: 'Performant, flexible forms with easy-to-use validation', ...getTechnologyIcon('React Hook Form')! },
    { name: 'Zod', version: '3.23.8', description: 'TypeScript-first schema validation with static type inference', ...getTechnologyIcon('Zod')! },

    // Build Tools & Utilities
    { name: 'Vite', version: 'Latest', description: 'Next-generation frontend tooling for an optimized dev experience', ...getTechnologyIcon('Vite')! },
    { name: 'Class Variance Authority', version: '0.7.1', description: 'Create type-safe, composable UI component variants', ...getTechnologyIcon('Class Variance Authority')! },
    { name: 'clsx', version: '2.1.1', description: 'A tiny utility for constructing conditional className strings', ...getTechnologyIcon('clsx')! },
    { name: 'Tailwind Merge', version: '2.5.2', description: 'Merge Tailwind CSS classes without style conflicts', ...getTechnologyIcon('Tailwind Merge')! },
    { name: 'tailwindcss-animate', version: '1.0.7', description: 'A Tailwind CSS plugin for orchestrating CSS animations', ...getTechnologyIcon('tailwindcss-animate')! }
  ];

  // Custom content for My Computer
  const renderCustomContent = () => (
    <>
      {/* Page Title Section */}
      <div className="mb-6 bg-gradient-to-r from-blue-50 to-white p-4 border-l-4 border-blue-500">
        <h1 className="text-xl font-bold text-blue-800 mb-1">System Information</h1>
        <p className="text-sm text-gray-600">Portfolio Application Tech Stack & Codebase Analysis</p>
      </div>

      {/* Codebase Composition Pie Chart */}
      <div className="mb-6">
        <CodebasePieChart />
      </div>

      <div className="grid grid-cols-1 gap-4 mb-6">
        <div className="bg-blue-50 p-3 rounded border-l-4 border-blue-500">
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div><strong>System:</strong> Windows XP Portfolio Theme</div>
            <div><strong>Architecture:</strong> Single Page Application</div>
            <div><strong>Runtime:</strong> Modern Web and Mobile Browsers</div>
            <div><strong>Build System:</strong> Vite + TypeScript</div>
          </div>
        </div>
      </div>

      {/* Tech Stack Grid */}
      <div className="mb-6">
        <h3 className="font-bold text-blue-800 mb-4 pb-2 border-b-2 border-blue-300 bg-gradient-to-r from-blue-100 to-transparent px-3 py-2 text-lg">
          Technology Stack
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {techStack.map((tech, index) => {
            const IconComponent = tech.icon;
            return (
              <div key={index} className="bg-white border-2 border-gray-300 p-4 hover:bg-blue-50 transition-colors cursor-pointer"
                   style={{ borderStyle: 'outset' }}>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <IconComponent className={`w-6 h-6 ${tech.color}`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start mb-1">
                      <h4 className="font-semibold text-gray-900 text-sm truncate">{tech.name}</h4>
                      <span className="text-xs text-gray-500 ml-2 flex-shrink-0">{tech.version}</span>
                    </div>
                    <p className="text-xs text-gray-600 leading-relaxed">{tech.description}</p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <div className="mt-6 p-4 bg-gradient-to-r from-blue-100 to-purple-100 rounded border">
        <h3 className="font-bold text-gray-800 mb-2 flex items-center">
          Performance Features
        </h3>
        <ul className="text-sm space-y-1 text-gray-700">
          <li>• Hot Module Replacement (HMR) for instant updates via Vite</li>
          <li>• Tree-shaking for optimized bundle size</li>
          <li>• TypeScript for enhanced developer experience and type safety</li>
          <li>• Component-based architecture for reusability and maintainability</li>
          <li>• Utility-first styling with Tailwind for efficient, consistent design</li>
        </ul>
      </div>
    </>
  );

  return (
    <FolderWindow
      title="My Computer"
      icon={<Computer size={16} className="text-blue-600" />}
      customContent={renderCustomContent()}
      sidebarConfig={sidebarConfig}
      showSidebar={true}
      onOpenWindow={onOpenWindow}
      variant="default"
      showNavigationButtons={true}
      showToolbar={true}
      showAddressBar={true}
      onBackClick={() => console.log('Navigate back')}
      onFoldersClick={() => console.log('Toggle folders panel')}
    />
  );
};

export default MyComputerContent;
