import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { useIsMobile } from '../hooks/use-mobile';
import { useMobileNavigationSafe } from '../contexts/MobileNavigationContext';

interface MobileBackButtonProps {
  onBackClick?: () => void;
  className?: string;
  showOnlyWhenCanGoBack?: boolean;
}

/**
 * Mobile Back Button component that provides a visual back button for mobile devices
 * This complements the browser back button functionality and can be used for testing
 */
const MobileBackButton: React.FC<MobileBackButtonProps> = ({
  onBackClick,
  className = '',
  showOnlyWhenCanGoBack = true
}) => {
  const isMobile = useIsMobile();
  const mobileNavigation = useMobileNavigationSafe();

  // Don't render on desktop or if navigation context is not available
  if (!isMobile || !mobileNavigation) {
    return null;
  }

  // Optionally hide when there's nothing to go back to
  if (showOnlyWhenCanGoBack && !mobileNavigation.canGoBack) {
    return null;
  }

  const handleClick = () => {
    if (onBackClick) {
      onBackClick();
    } else {
      // Trigger browser back button behavior
      window.history.back();
    }
  };

  return (
    <button
      onClick={handleClick}
      className={`
        fixed top-4 left-4 z-50
        bg-blue-600 hover:bg-blue-700 active:bg-blue-800
        text-white rounded-full p-3 shadow-lg
        transition-all duration-200
        flex items-center justify-center
        ${className}
      `}
      style={{
        background: 'linear-gradient(to bottom, #4a90e2 0%, #357abd 50%, #1e5f99 100%)',
        border: '1px solid #2c5aa0',
        boxShadow: '0 4px 8px rgba(0,0,0,0.3), inset 1px 1px 2px rgba(255,255,255,0.3)'
      }}
      title="Go Back"
      aria-label="Go Back"
    >
      <ArrowLeft size={20} />
    </button>
  );
};

export default MobileBackButton;
