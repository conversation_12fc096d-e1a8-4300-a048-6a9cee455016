
import React from 'react';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { getTechnologyIcon } from '@shared/icons/TechnologyIcons';

// Use the shared technology icon registry instead of local iconMap

interface SkillIconProps {
  skill: string;
}

const SkillIcon: React.FC<SkillIconProps> = ({ skill }) => {
  const skillData = getTechnologyIcon(skill);

  // Only render if an icon is available
  if (!skillData) {
    return null;
  }

  const { icon: IconComponent, color } = skillData;

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-white/80 dark:bg-white/5 border border-gray-200/50 dark:border-white/10 cursor-pointer transition-all duration-300 hover:bg-white dark:hover:bg-white/10 hover:scale-105 hover:shadow-lg hover:border-gray-300 dark:hover:border-white/20">
          <IconComponent className={`w-4 h-4 md:w-5 md:h-5 transition-transform ${color}`} />
          <span className="text-sm md:text-base font-medium text-gray-700 dark:text-gray-300">
            {skill}
          </span>
        </div>
      </TooltipTrigger>
      <TooltipContent>
        <p>{skill}</p>
      </TooltipContent>
    </Tooltip>
  );
};

export default SkillIcon;
