
import { useState, useEffect } from 'react';
import { Linkedin, Mail, Monitor } from 'lucide-react';
import { ShutdownType } from '../hooks/useShutdown';

interface ShutdownSequenceProps {
  onComplete?: () => void;
  type: ShutdownType | null;
}

const ShutdownSequence = ({ onComplete, type }: ShutdownSequenceProps) => {
  const [stage, setStage] = useState<'dialog' | 'thank-you' | 'fade' | 'contacts'>(
    type === 'logoff' ? 'contacts' : 'dialog'
  );

  useEffect(() => {
    if (type === 'shutdown') {
      const timer1 = setTimeout(() => {
        setStage('thank-you');
      }, 2000);

      const timer2 = setTimeout(() => {
        setStage('fade');
      }, 4000);

      const timer3 = setTimeout(() => {
        setStage('contacts');
      }, 5000);

      return () => {
        clearTimeout(timer1);
        clearTimeout(timer2);
        clearTimeout(timer3);
      };
    }
  }, [type]);

  const handleLinkedInClick = () => {
    window.open('https://linkedin.com/in/markoverano', '_blank');
  };

  const handleEmailClick = () => {
    window.open('mailto:<EMAIL>', '_blank');
  };

  const handleReturnToDesktop = () => {
    if (onComplete) {
      onComplete();
    }
  };

  return (
    <div className="fixed inset-0 z-[9999] bg-black flex items-center justify-center">
      {/* Classic XP Shutdown Dialog */}
      {stage === 'dialog' && (
        <div className="bg-gray-200 border-2 border-gray-400 shadow-2xl p-8 rounded animate-fade-in">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-blue-500 rounded flex items-center justify-center">
              <span className="text-white text-xl">💻</span>
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-800">Windows</h2>
              <p className="text-sm text-gray-600">It's now safe to turn off your computer</p>
            </div>
          </div>
        </div>
      )}

      {/* Thank You Message */}
      {stage === 'thank-you' && (
        <div className="text-center animate-fade-in">
          <h1 className="text-4xl font-bold text-white mb-4">Thank You!</h1>
          <p className="text-xl text-gray-300 mb-2">
            Thank you for visiting my portfolio.
          </p>
          <p className="text-lg text-gray-400">
            Hope to connect soon!
          </p>
        </div>
      )}

      {/* Fade Stage */}
      {stage === 'fade' && (
        <div className="w-full h-full bg-black animate-fade-in"></div>
      )}

      {/* Contact Information */}
      {stage === 'contacts' && (
        <div className="text-center animate-fade-in">
          <p className="text-gray-400 mb-8 text-lg">Let's connect:</p>
          <div className="flex justify-center space-x-8 mb-8">
            <button
              onClick={handleLinkedInClick}
              className="flex items-center space-x-2 text-blue-400 hover:text-blue-300 transition-colors duration-200 hover:scale-105 transform"
            >
              <Linkedin className="w-8 h-8" />
              <span className="text-lg">LinkedIn</span>
            </button>
            <button
              onClick={handleEmailClick}
              className="flex items-center space-x-2 text-green-400 hover:text-green-300 transition-colors duration-200 hover:scale-105 transform"
            >
              <Mail className="w-8 h-8" />
              <span className="text-lg">Email</span>
            </button>
          </div>
          
          {/* Return to Desktop Button */}
          <div className="border-t border-gray-700 pt-6">
            <button
              onClick={handleReturnToDesktop}
              className="flex items-center space-x-2 text-yellow-400 hover:text-yellow-300 transition-colors duration-200 hover:scale-105 transform mx-auto"
            >
              <Monitor className="w-8 h-8" />
              <span className="text-lg">Return to Desktop</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ShutdownSequence;
