/**
 * Desktop Icon Position Management Utilities
 * Handles positioning, persistence, and collision detection for Windows XP-style desktop icons
 */

export interface IconPosition {
  x: number;
  y: number;
}

export interface IconDimensions {
  width: number;
  height: number;
}

export interface PositionedIcon {
  id: string;
  position: IconPosition;
  dimensions: IconDimensions;
}

// Constants for icon layout
export const ICON_CONSTANTS = {
  WIDTH: 80, // 20 * 4 (w-20 class)
  HEIGHT: 80, // Approximate height including text
  GRID_SPACING: 16, // gap-y-4 equivalent
  DESKTOP_PADDING: 16, // p-4 equivalent
  TASKBAR_HEIGHT: 40, // bottom-10 equivalent
  COLLISION_THRESHOLD: 10, // Minimum distance between icons
} as const;

// Default icon positions (left column layout like Windows XP)
const DEFAULT_POSITIONS: Record<string, IconPosition> = {
  my_computer: { x: 16, y: 16 },
  projects: { x: 16, y: 112 },
  my_resume: { x: 16, y: 208 },
  skills: { x: 16, y: 304 },
  certifications: { x: 16, y: 400 },
  contact: { x: 16, y: 496 },
  about: { x: 16, y: 592 },
  recyclebin: { x: 16, y: 688 },
};

// SessionStorage key for icon positions
const STORAGE_KEY = 'desktop_icon_positions';

/**
 * Get stored icon positions from sessionStorage
 */
export function getStoredIconPositions(): Record<string, IconPosition> {
  try {
    const stored = sessionStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.warn('Failed to parse stored icon positions:', error);
    return {};
  }
}

/**
 * Store icon positions in sessionStorage
 */
export function storeIconPositions(positions: Record<string, IconPosition>): void {
  try {
    sessionStorage.setItem(STORAGE_KEY, JSON.stringify(positions));
  } catch (error) {
    console.warn('Failed to store icon positions:', error);
  }
}

/**
 * Get the current position for an icon (stored or default)
 */
export function getIconPosition(iconId: string): IconPosition {
  const storedPositions = getStoredIconPositions();
  return storedPositions[iconId] || DEFAULT_POSITIONS[iconId] || { x: 16, y: 16 };
}

/**
 * Update the position of a specific icon
 */
export function updateIconPosition(iconId: string, position: IconPosition): void {
  const currentPositions = getStoredIconPositions();
  currentPositions[iconId] = position;
  storeIconPositions(currentPositions);
}

/**
 * Reset all icon positions to defaults (clear sessionStorage)
 */
export function resetIconPositions(): void {
  try {
    sessionStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.warn('Failed to reset icon positions:', error);
  }
}

/**
 * Get desktop boundaries for icon positioning
 */
export function getDesktopBoundaries(): { width: number; height: number } {
  const width = globalThis.window?.innerWidth || 1024;
  const height = (globalThis.window?.innerHeight || 768) - ICON_CONSTANTS.TASKBAR_HEIGHT;
  
  return {
    width: width - ICON_CONSTANTS.DESKTOP_PADDING - ICON_CONSTANTS.WIDTH,
    height: height - ICON_CONSTANTS.DESKTOP_PADDING - ICON_CONSTANTS.HEIGHT,
  };
}

/**
 * Constrain position to desktop boundaries
 */
export function constrainToDesktop(position: IconPosition): IconPosition {
  const boundaries = getDesktopBoundaries();
  
  return {
    x: Math.max(ICON_CONSTANTS.DESKTOP_PADDING, Math.min(position.x, boundaries.width)),
    y: Math.max(ICON_CONSTANTS.DESKTOP_PADDING, Math.min(position.y, boundaries.height)),
  };
}

/**
 * Check if two icons would collide at given positions
 */
export function checkIconCollision(
  pos1: IconPosition,
  pos2: IconPosition,
  threshold: number = ICON_CONSTANTS.COLLISION_THRESHOLD
): boolean {
  const dx = Math.abs(pos1.x - pos2.x);
  const dy = Math.abs(pos1.y - pos2.y);
  
  return dx < (ICON_CONSTANTS.WIDTH + threshold) && dy < (ICON_CONSTANTS.HEIGHT + threshold);
}

/**
 * Find the nearest valid position that doesn't collide with other icons
 */
export function findValidPosition(
  targetPosition: IconPosition,
  existingIcons: PositionedIcon[],
  excludeIconId?: string
): IconPosition {
  let position = constrainToDesktop(targetPosition);
  
  // Check for collisions with existing icons
  const otherIcons = existingIcons.filter(icon => icon.id !== excludeIconId);
  
  // If no collision, return the constrained position
  const hasCollision = otherIcons.some(icon => 
    checkIconCollision(position, icon.position)
  );
  
  if (!hasCollision) {
    return position;
  }
  
  // Try to find a nearby valid position
  const searchRadius = ICON_CONSTANTS.WIDTH + ICON_CONSTANTS.COLLISION_THRESHOLD;
  const maxAttempts = 20;
  
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    const angle = (attempt * Math.PI * 2) / maxAttempts;
    const distance = searchRadius * (1 + attempt * 0.5);
    
    const candidatePosition = constrainToDesktop({
      x: targetPosition.x + Math.cos(angle) * distance,
      y: targetPosition.y + Math.sin(angle) * distance,
    });
    
    const candidateHasCollision = otherIcons.some(icon => 
      checkIconCollision(candidatePosition, icon.position)
    );
    
    if (!candidateHasCollision) {
      return candidatePosition;
    }
  }
  
  // If no valid position found, return the constrained original position
  // This should rarely happen, but provides a fallback
  return position;
}

/**
 * Get all current icon positions for collision detection
 */
export function getAllIconPositions(iconIds: string[]): PositionedIcon[] {
  return iconIds.map(id => ({
    id,
    position: getIconPosition(id),
    dimensions: {
      width: ICON_CONSTANTS.WIDTH,
      height: ICON_CONSTANTS.HEIGHT,
    },
  }));
}

/**
 * Initialize icon positions on app start
 * This can be called to ensure all icons have valid positions
 */
export function initializeIconPositions(iconIds: string[]): void {
  const storedPositions = getStoredIconPositions();
  let hasChanges = false;
  
  // Ensure all icons have positions
  iconIds.forEach(iconId => {
    if (!storedPositions[iconId] && DEFAULT_POSITIONS[iconId]) {
      storedPositions[iconId] = DEFAULT_POSITIONS[iconId];
      hasChanges = true;
    }
  });
  
  if (hasChanges) {
    storeIconPositions(storedPositions);
  }
}
