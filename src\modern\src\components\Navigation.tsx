import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Monitor,
  Briefcase,
  Code,
  FolderOpen,
  Menu,
  Zap,
} from "lucide-react";
import ThemeToggle from "@shared/components/ThemeToggle";

interface NavigationProps {
  showMobileNav: boolean;
  onToggleMobileNav: () => void;
  onScrollToSection: (sectionName: string) => void;
}

const Navigation: React.FC<NavigationProps> = ({
  showMobileNav,
  onToggleMobileNav,
  onScrollToSection,
}) => {

  const navItems = [
    { name: "profile", label: "Profile", icon: Monitor },
    { name: "projects", label: "Projects", icon: FolderOpen },
    { name: "skills", label: "Skills", icon: Code },
    { name: "experience", label: "Experience", icon: Briefcase },
  ];

  return (
    <>
      {/* Fixed Navigation - Desktop */}
      <div className="fixed top-[6px] right-6 z-50 hidden md:block">
        <div
          className="flex items-center gap-2 bg-slate-100/80 dark:bg-white/10 backdrop-blur-xl border border-slate-200/80 dark:border-white/20 rounded-2xl py-[5.5px] px-2 shadow-2xl shadow-black/20"
        >
          {navItems.map((item) => {
            const IconComponent = item.icon;
            return (
              <button
                key={item.name}
                onClick={() => onScrollToSection(item.name)}
                className="group flex items-center gap-2 px-4 py-3 rounded-xl font-medium text-sm text-slate-700 dark:text-white/80 hover:text-slate-900 dark:hover:text-white hover:bg-slate-200/50 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105"
              >
                <IconComponent className="w-4 h-4 text-slate-500 dark:text-white/80 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-colors" />
                {item.label}
                <div className="w-0 group-hover:w-1 h-1 bg-blue-400 rounded-full transition-all duration-300" />
              </button>
            );
          })}

          {/* Decorative element */}
          <div className="w-px h-8 bg-slate-300 dark:bg-white/20 mx-2" />
          <div className="flex items-center gap-1 px-2">
            {/* Computer/XP Theme Button */}
            <ThemeToggle
              variant="xp-modern"
              position="inline"
              showLabels={true}
              size="sm"
            />
          </div>
        </div>
      </div>

      {/* Mobile Navigation Button */}
      <div className="fixed top-[19px] right-6 z-50 md:hidden">
        <Button
          onClick={onToggleMobileNav}
          className="w-12 h-12 rounded-full bg-slate-100/80 dark:bg-white/10 backdrop-blur-xl border border-slate-200/80 dark:border-white/20 shadow-2xl hover:bg-slate-200/90 dark:hover:bg-white/20 text-slate-700 dark:text-white"
          size="sm"
          variant="ghost"
        >
          <Menu className="w-5 h-5" />
        </Button>
      </div>

      {/* Mobile Navigation Menu */}
      {showMobileNav && (
        <div className="fixed top-20 right-6 z-50 md:hidden animate-fade-in">
          <div className="bg-slate-100/80 dark:bg-white/10 backdrop-blur-xl border border-slate-200/80 dark:border-white/20 rounded-2xl p-3 shadow-2xl shadow-black/20 min-w-[160px]">
            {navItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <button
                  key={item.name}
                  onClick={() => onScrollToSection(item.name)}
                  className="flex items-center gap-3 w-full px-4 py-3 rounded-xl font-medium text-sm text-slate-700 dark:text-white/80 hover:text-slate-900 dark:hover:text-white hover:bg-slate-200/50 dark:hover:bg-white/20 transition-all duration-300 text-left"
                >
                  <IconComponent className="w-4 h-4 text-slate-500 dark:text-white/80" />
                  {item.label}
                </button>
              );
            })}

            {/* Computer/XP Theme Button for Mobile */}
            <div className="border-t border-slate-200/50 dark:border-white/10 mt-2 pt-3">
              <ThemeToggle
                variant="xp-modern"
                position="inline"
                showLabels={true}
                size="sm"
                className="w-full justify-start"
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Navigation;
