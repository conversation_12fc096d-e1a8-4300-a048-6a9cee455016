/**
 * Test file for dialog management system
 * This demonstrates the key functionality and prevents regressions
 */

import { renderHook, act } from '@testing-library/react';
import { useDialogManager } from '../hooks/useDialogManager';

describe('Dialog Manager', () => {
  test('should prevent multiple instances of same dialog type', () => {
    const { result } = renderHook(() => useDialogManager());
    
    act(() => {
      // Open first dialog
      const id1 = result.current.openDialog('test', { message: 'First' });
      expect(id1).toBeTruthy();
      
      // Try to open second dialog of same type
      const id2 = result.current.openDialog('test', { message: 'Second' });
      expect(id2).toBe(id1); // Should return same ID (brought to front)
      
      // Should only have one dialog
      expect(result.current.getDialogCount('test')).toBe(1);
    });
  });

  test('should allow multiple instances when specified', () => {
    const { result } = renderHook(() => useDialogManager());
    
    act(() => {
      const id1 = result.current.openDialog('test', { message: 'First' });
      const id2 = result.current.openDialog('test', { message: 'Second' }, { allowMultiple: true });
      
      expect(id1).not.toBe(id2);
      expect(result.current.getDialogCount('test')).toBe(2);
    });
  });

  test('should respect max dialog limit', () => {
    const { result } = renderHook(() => useDialogManager({ maxDialogs: 2 }));
    
    act(() => {
      const id1 = result.current.openDialog('test1', {});
      const id2 = result.current.openDialog('test2', {});
      const id3 = result.current.openDialog('test3', {}); // Should be rejected
      
      expect(id1).toBeTruthy();
      expect(id2).toBeTruthy();
      expect(id3).toBeNull();
      expect(result.current.getDialogCount()).toBe(2);
    });
  });

  test('should close dialogs correctly', () => {
    const { result } = renderHook(() => useDialogManager());
    
    act(() => {
      const id = result.current.openDialog('test', {});
      expect(result.current.isDialogOpen('test')).toBe(true);
      
      result.current.closeDialog('test');
      expect(result.current.isDialogOpen('test')).toBe(false);
    });
  });

  test('should manage z-index correctly', () => {
    const { result } = renderHook(() => useDialogManager({ baseZIndex: 1000 }));
    
    act(() => {
      result.current.openDialog('test1', {});
      result.current.openDialog('test2', {}, { allowMultiple: true });
      
      const dialogs = result.current.dialogs;
      expect(dialogs[0].zIndex).toBe(1001);
      expect(dialogs[1].zIndex).toBe(1002);
    });
  });

  test('should bring dialog to front', () => {
    const { result } = renderHook(() => useDialogManager());
    
    act(() => {
      const id1 = result.current.openDialog('test1', {});
      const id2 = result.current.openDialog('test2', {}, { allowMultiple: true });
      
      // Bring first dialog to front
      result.current.bringToFront(id1!);
      
      const dialogs = result.current.dialogs;
      const dialog1 = dialogs.find(d => d.id === id1);
      const dialog2 = dialogs.find(d => d.id === id2);
      
      expect(dialog1!.zIndex).toBeGreaterThan(dialog2!.zIndex);
    });
  });

  test('should close all dialogs', () => {
    const { result } = renderHook(() => useDialogManager());
    
    act(() => {
      result.current.openDialog('test1', {});
      result.current.openDialog('test2', {}, { allowMultiple: true });
      expect(result.current.getDialogCount()).toBe(2);
      
      result.current.closeAllDialogs();
      expect(result.current.getDialogCount()).toBe(0);
    });
  });
});

// Integration test for utility functions
describe('Dialog Utilities', () => {
  test('should provide safe dialog operations', () => {
    // These tests would require a more complex setup with React context
    // For now, we verify the functions exist and don't throw
    const { safeOpenDialog, safeCloseDialog, isDialogCurrentlyOpen } = require('../hooks/useDialogManager');
    
    expect(typeof safeOpenDialog).toBe('function');
    expect(typeof safeCloseDialog).toBe('function');
    expect(typeof isDialogCurrentlyOpen).toBe('function');
    
    // Without a manager, these should handle gracefully
    expect(safeOpenDialog('test', {})).toBeNull();
    expect(isDialogCurrentlyOpen('test')).toBe(false);
    
    // Should not throw
    expect(() => safeCloseDialog('test')).not.toThrow();
  });
});
