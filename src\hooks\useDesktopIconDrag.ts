/**
 * Desktop Icon Drag Hook
 * Handles drag-and-drop functionality for Windows XP-style desktop icons
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  IconPosition,
  PositionedIcon,
  updateIconPosition,
  findValidPosition,
  constrainToDesktop,
  getAllIconPositions,
} from '../utils/iconPositionManager';

export interface UseDesktopIconDragProps {
  iconId: string;
  currentPosition: IconPosition;
  allIconIds: string[];
  onPositionUpdate?: (iconId: string, position: IconPosition) => void;
  isMobile?: boolean;
}

export interface DragState {
  isDragging: boolean;
  dragOffset: IconPosition;
  currentPosition: IconPosition;
  isValidPosition: boolean;
}

export const useDesktopIconDrag = ({
  iconId,
  currentPosition,
  allIconIds,
  onPositionUpdate,
  isMobile = false,
}: UseDesktopIconDragProps) => {
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    dragOffset: { x: 0, y: 0 },
    currentPosition,
    isValidPosition: true,
  });

  const dragStartTimeRef = useRef<number>(0);
  const hasMovedRef = useRef<boolean>(false);

  // Update current position when prop changes
  useEffect(() => {
    if (!dragState.isDragging) {
      setDragState(prev => ({
        ...prev,
        currentPosition,
      }));
    }
  }, [currentPosition, dragState.isDragging]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (isMobile) return;

    // Prevent default to avoid text selection
    e.preventDefault();

    // Record drag start time and reset movement flag
    dragStartTimeRef.current = Date.now();
    hasMovedRef.current = false;

    // Don't set isDragging to true immediately - wait for actual movement
    setDragState(prev => ({
      ...prev,
      isDragging: false, // Start as false, will be set to true on first movement
      dragOffset: {
        x: e.clientX - currentPosition.x,
        y: e.clientY - currentPosition.y,
      },
    }));
  }, [currentPosition, isMobile]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!isMobile) return;

    // Prevent default to avoid scrolling
    e.preventDefault();

    const touch = e.touches[0];
    if (!touch) return;

    // Record drag start time and reset movement flag
    dragStartTimeRef.current = Date.now();
    hasMovedRef.current = false;

    // Don't set isDragging to true immediately - wait for actual movement
    setDragState(prev => ({
      ...prev,
      isDragging: false, // Start as false, will be set to true on first movement
      dragOffset: {
        x: touch.clientX - currentPosition.x,
        y: touch.clientY - currentPosition.y,
      },
    }));
  }, [currentPosition, isMobile]);

  // Check if the current drag position is valid (no collisions)
  const checkPositionValidity = useCallback((position: IconPosition): boolean => {
    const allIcons = getAllIconPositions(allIconIds);
    const otherIcons = allIcons.filter(icon => icon.id !== iconId);
    
    return !otherIcons.some(icon => {
      const dx = Math.abs(position.x - icon.position.x);
      const dy = Math.abs(position.y - icon.position.y);
      return dx < 70 && dy < 70; // Collision threshold
    });
  }, [allIconIds, iconId]);

  useEffect(() => {
    // Check if we have drag offset set (mouse/touch down occurred)
    const hasDragOffset = dragState.dragOffset.x !== 0 || dragState.dragOffset.y !== 0;
    if (!hasDragOffset) return;

    const handleMouseMove = (e: MouseEvent) => {
      const newPosition = constrainToDesktop({
        x: e.clientX - dragState.dragOffset.x,
        y: e.clientY - dragState.dragOffset.y,
      });

      // Calculate movement distance to determine if this is a real drag
      const moveDistance = Math.sqrt(
        Math.pow(newPosition.x - currentPosition.x, 2) +
        Math.pow(newPosition.y - currentPosition.y, 2)
      );

      // Only start dragging if moved more than threshold (5 pixels)
      if (moveDistance > 5) {
        hasMovedRef.current = true;

        const isValid = checkPositionValidity(newPosition);

        setDragState(prev => ({
          ...prev,
          isDragging: true, // Now we can set this to true
          currentPosition: newPosition,
          isValidPosition: isValid,
        }));
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      e.preventDefault(); // Prevent scrolling

      const touch = e.touches[0];
      if (!touch) return;

      const newPosition = constrainToDesktop({
        x: touch.clientX - dragState.dragOffset.x,
        y: touch.clientY - dragState.dragOffset.y,
      });

      // Calculate movement distance to determine if this is a real drag
      const moveDistance = Math.sqrt(
        Math.pow(newPosition.x - currentPosition.x, 2) +
        Math.pow(newPosition.y - currentPosition.y, 2)
      );

      // Only start dragging if moved more than threshold (5 pixels)
      if (moveDistance > 5) {
        hasMovedRef.current = true;

        const isValid = checkPositionValidity(newPosition);

        setDragState(prev => ({
          ...prev,
          isDragging: true, // Now we can set this to true
          currentPosition: newPosition,
          isValidPosition: isValid,
        }));
      }
    };

    const handleEnd = () => {
      const dragDuration = Date.now() - dragStartTimeRef.current;
      const wasDragOperation = hasMovedRef.current && dragDuration > 100 && dragState.isDragging;

      if (wasDragOperation) {
        // Find the best valid position for the icon
        const allIcons = getAllIconPositions(allIconIds);
        const finalPosition = findValidPosition(
          dragState.currentPosition,
          allIcons,
          iconId
        );

        // Update the icon position in storage
        updateIconPosition(iconId, finalPosition);

        // Notify parent component
        onPositionUpdate?.(iconId, finalPosition);

        setDragState(prev => ({
          ...prev,
          isDragging: false,
          currentPosition: finalPosition,
          isValidPosition: true,
          dragOffset: { x: 0, y: 0 }, // Reset drag offset
        }));
      } else {
        // Reset to original position if it wasn't a real drag
        setDragState(prev => ({
          ...prev,
          isDragging: false,
          currentPosition,
          isValidPosition: true,
          dragOffset: { x: 0, y: 0 }, // Reset drag offset
        }));
      }
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleEnd);
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleEnd);

    // Cleanup
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleEnd);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleEnd);
    };
  }, [
    dragState.isDragging,
    dragState.dragOffset,
    dragState.currentPosition,
    allIconIds,
    iconId,
    currentPosition,
    onPositionUpdate,
    checkPositionValidity,
  ]);

  // Determine if this was a click vs drag for double-click handling
  const wasClick = useCallback((): boolean => {
    const dragDuration = Date.now() - dragStartTimeRef.current;
    // It's a click if: no movement occurred, short duration, and never entered drag state
    return !hasMovedRef.current && dragDuration < 300 && !dragState.isDragging;
  }, [dragState.isDragging]);

  return {
    dragState,
    handleMouseDown,
    handleTouchStart,
    wasClick,
  };
};
