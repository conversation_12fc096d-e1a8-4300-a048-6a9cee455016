# Resend Email Integration Setup Guide

This guide explains how to set up and deploy the Resend email integration for your Windows XP Portfolio website.

## Overview

The email system consists of:
- **Frontend**: Enhanced `OutlookMailContent` component with rate limiting
- **Backend**: Cloudflare Worker that securely handles email sending via Resend API
- **Rate Limiting**: Session-based limiting (3 emails per browser session)

## Prerequisites

1. **Resend Account**: Sign up at [resend.com](https://resend.com)
2. **Cloudflare Account**: Sign up at [cloudflare.com](https://cloudflare.com)
3. **Domain**: Your domain should be managed by Cloudflare

## Step 1: Resend Configuration

1. **Get API Key**:
   - Log into your Resend dashboard
   - Go to API Keys section
   - Create a new API key
   - Copy the key (starts with `re_`)

2. **Verify Domain** (Optional but recommended):
   - Add your domain `markoverano.dev` to Resend
   - Follow DNS verification steps
   - This allows sending from `<EMAIL>`

## Step 2: Cloudflare Worker Setup

1. **Install Wrangler CLI**:
   ```bash
   npm install -g wrangler
   ```

2. **Login to Cloudflare**:
   ```bash
   wrangler login
   ```

3. **Navigate to Worker Directory**:
   ```bash
   cd workers/email-sender
   npm install
   ```

4. **Set API Key as Secret**:
   ```bash
   wrangler secret put RESEND_API_KEY
   # Enter your Resend API key when prompted
   ```

5. **Update Domain in wrangler.toml**:
   ```toml
   [[env.production.routes]]
   pattern = "markoverano.dev/api/send-email"
   zone_name = "markoverano.dev"
   ```

6. **Deploy Worker**:
   ```bash
   npm run deploy
   ```

## Step 3: Frontend Configuration

The frontend is already configured to:
- Use `/api/send-email` endpoint in production
- Proxy to `localhost:8787` in development
- Handle rate limiting with sessionStorage
- Maintain Windows XP styling

## Step 4: Testing

### Local Development Testing

1. **Start Cloudflare Worker locally**:
   ```bash
   cd workers/email-sender
   npm run dev
   ```

2. **Start Vite dev server** (in another terminal):
   ```bash
   npm run dev
   ```

3. **Test the contact form**:
   - Open `http://localhost:8080`
   - Open Outlook Mail window
   - Fill out the form and send a test email

### Production Testing

1. **Deploy your main site** to Cloudflare Pages or your hosting provider
2. **Ensure the Worker is deployed** and routes are configured
3. **Test the contact form** on your live site

## Step 5: Monitoring

1. **Cloudflare Dashboard**:
   - Monitor Worker requests and errors
   - Check logs with `wrangler tail`

2. **Resend Dashboard**:
   - Monitor email delivery status
   - Check for bounces or delivery issues

## Troubleshooting

### Common Issues

1. **CORS Errors**:
   - Ensure Worker includes proper CORS headers
   - Check that routes are configured correctly

2. **API Key Issues**:
   - Verify the secret is set: `wrangler secret list`
   - Ensure the key is valid in Resend dashboard

3. **Route Not Found**:
   - Check `wrangler.toml` routes configuration
   - Ensure domain is added to Cloudflare

4. **Email Not Delivered**:
   - Check Resend dashboard for delivery status
   - Verify recipient email address
   - Check spam folder

### Development Issues

1. **Worker Not Starting**:
   ```bash
   cd workers/email-sender
   npm install
   npm run dev
   ```

2. **Proxy Not Working**:
   - Ensure Vite proxy is configured in `vite.config.ts`
   - Check that Worker is running on port 8787

## Security Features

- ✅ API key stored securely as Cloudflare Worker secret
- ✅ Rate limiting (3 emails per session)
- ✅ Input validation and sanitization
- ✅ CORS protection
- ✅ No sensitive data in client-side code

## Rate Limiting Details

- **Limit**: 3 emails per browser session
- **Storage**: sessionStorage (resets on browser restart)
- **UI**: Shows remaining emails and limit reached message
- **Reset**: Refresh browser to reset counter

## Email Template

Emails are sent with:
- **From**: `Portfolio Contact <<EMAIL>>`
- **To**: `<EMAIL>`
- **Reply-To**: User's email address
- **Subject**: `Portfolio Contact: [User Subject]`
- **Format**: HTML with Windows XP-inspired styling

## Fallback Option

The system includes an EmailJS fallback in `emailService.ts` for backup/testing purposes. This can be removed once Resend is fully tested and working.

## Next Steps

1. Deploy the Cloudflare Worker
2. Test email functionality thoroughly
3. Monitor delivery rates and performance
4. Remove EmailJS dependency once confirmed working
5. Consider adding email templates for different types of inquiries
