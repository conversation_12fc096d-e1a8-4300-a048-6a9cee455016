import React from 'react';

// React Icons imports - consolidated from both themes
import {
  SiReact, SiTypescript, SiTailwindcss, SiVite, SiRadixui,
  SiLucide, SiReactquery, SiZod, SiJavascript, SiHtml5, SiCss3,
  SiNodedotjs, SiAngular, SiMysql, SiPostgresql, SiDocker,
  SiGit, SiDotnet, SiSharp, SiAsana, SiBitbucket, SiConfluence,
  SiDevexpress, SiElasticsearch, SiGitlab, SiJira, SiJson,
  SiMetabase, SiNotion, SiPostman, SiSqlite, SiSubversion, SiXml
} from 'react-icons/si';

import {
  FaRoute, FaWpforms, FaChartBar, FaBell, FaExpandArrowsAlt,
  FaPalette, FaCode, FaCodeBranch, FaDatabase, FaNodeJs, FaTrello
} from 'react-icons/fa';

import {
  DiAngularSimple, DiCss3, <PERSON>Dock<PERSON>, DiDotnet, DiGithubBadge,
  DiHtml5, DiJqueryLogo, DiMysql, DiReact, DiVisualstudio, DiPostgresql
} from 'react-icons/di';

import { TbBrandFramerMotion } from 'react-icons/tb';
import { VscVscode, VscJson } from 'react-icons/vsc';

export interface TechnologyIcon {
  icon: React.ElementType;
  color: string;
  category?: 'language' | 'framework' | 'tool' | 'database' | 'ui' | 'build';
}

// Comprehensive technology icon registry
export const technologyIcons: { [key: string]: TechnologyIcon } = {
  // Core Frontend Technologies
  'React': { icon: SiReact, color: 'text-cyan-500', category: 'framework' },
  'TypeScript': { icon: SiTypescript, color: 'text-blue-600', category: 'language' },
  'JavaScript': { icon: SiJavascript, color: 'text-yellow-500', category: 'language' },
  'HTML5': { icon: SiHtml5, color: 'text-orange-600', category: 'language' },
  'CSS3': { icon: SiCss3, color: 'text-blue-500', category: 'language' },
  
  // Backend Technologies
  'Node.js': { icon: SiNodedotjs, color: 'text-green-600', category: 'framework' },
  'C#': { icon: SiSharp, color: 'text-purple-600', category: 'language' },
  'VB.NET': { icon: SiDotnet, color: 'text-blue-600', category: 'language' },
  'ASP.NET': { icon: SiDotnet, color: 'text-blue-500', category: 'framework' },
  'Razor': { icon: SiDotnet, color: 'text-blue-500', category: 'framework' },
  
  // Frameworks & Libraries
  'Angular': { icon: SiAngular, color: 'text-red-600', category: 'framework' },
  'React Router': { icon: FaRoute, color: 'text-red-500', category: 'framework' },
  'TanStack Query': { icon: SiReactquery, color: 'text-red-600', category: 'framework' },
  'React Hook Form': { icon: FaWpforms, color: 'text-blue-500', category: 'framework' },
  'Zod': { icon: SiZod, color: 'text-blue-700', category: 'framework' },
  
  // Styling & UI
  'Tailwind CSS': { icon: SiTailwindcss, color: 'text-cyan-400', category: 'ui' },
  'Shadcn/ui': { icon: SiRadixui, color: 'text-purple-600', category: 'ui' },
  'Lucide React': { icon: SiLucide, color: 'text-orange-500', category: 'ui' },
  'React Icons': { icon: FaPalette, color: 'text-pink-500', category: 'ui' },
  'tailwindcss-animate': { icon: TbBrandFramerMotion, color: 'text-purple-400', category: 'ui' },
  
  // Build Tools & Development
  'Vite': { icon: SiVite, color: 'text-purple-500', category: 'build' },
  'Git': { icon: SiGit, color: 'text-orange-600', category: 'tool' },
  'Visual Studio': { icon: DiVisualstudio, color: 'text-purple-600', category: 'tool' },
  'VS Code': { icon: VscVscode, color: 'text-blue-500', category: 'tool' },
  'Docker': { icon: SiDocker, color: 'text-blue-600', category: 'tool' },
  
  // Databases
  'MySQL': { icon: SiMysql, color: 'text-blue-700', category: 'database' },
  'PostgreSQL': { icon: SiPostgresql, color: 'text-blue-600', category: 'database' },
  'SQLite': { icon: SiSqlite, color: 'text-blue-500', category: 'database' },
  'T-SQL': { icon: FaDatabase, color: 'text-blue-700', category: 'database' },
  
  // Project Management & Collaboration
  'Jira': { icon: SiJira, color: 'text-blue-600', category: 'tool' },
  'Confluence': { icon: SiConfluence, color: 'text-blue-500', category: 'tool' },
  'Asana': { icon: SiAsana, color: 'text-red-500', category: 'tool' },
  'Trello': { icon: FaTrello, color: 'text-blue-500', category: 'tool' },
  'Notion': { icon: SiNotion, color: 'text-gray-800', category: 'tool' },
  
  // Version Control & DevOps
  'GitHub': { icon: DiGithubBadge, color: 'text-gray-800', category: 'tool' },
  'GitLab': { icon: SiGitlab, color: 'text-orange-600', category: 'tool' },
  'Bitbucket': { icon: SiBitbucket, color: 'text-blue-600', category: 'tool' },
  'Subversion': { icon: SiSubversion, color: 'text-blue-500', category: 'tool' },
  
  // Data & Analytics
  'Elasticsearch': { icon: SiElasticsearch, color: 'text-yellow-500', category: 'tool' },
  'Metabase': { icon: SiMetabase, color: 'text-blue-600', category: 'tool' },
  'Recharts': { icon: FaChartBar, color: 'text-green-600', category: 'ui' },
  
  // Development Tools
  'Postman': { icon: SiPostman, color: 'text-orange-500', category: 'tool' },
  'DevExpress': { icon: SiDevexpress, color: 'text-blue-600', category: 'ui' },
  'jQuery': { icon: DiJqueryLogo, color: 'text-blue-600', category: 'framework' },
  
  // Utilities & Misc
  'JSON': { icon: SiJson, color: 'text-yellow-600', category: 'language' },
  'XML': { icon: SiXml, color: 'text-orange-500', category: 'language' },
  'Sonner': { icon: FaBell, color: 'text-yellow-500', category: 'ui' },
  'React Resizable Panels': { icon: FaExpandArrowsAlt, color: 'text-indigo-500', category: 'ui' },
  'Class Variance Authority': { icon: FaCodeBranch, color: 'text-gray-600', category: 'tool' },
  'clsx': { icon: FaCode, color: 'text-green-500', category: 'tool' },
  'Tailwind Merge': { icon: FaCodeBranch, color: 'text-teal-500', category: 'tool' },

  // Additional technologies from SkillIcon component
  'LINQ': { icon: FaDatabase, color: 'text-purple-500', category: 'language' },
  'Web API': { icon: VscJson, color: 'text-yellow-600', category: 'framework' },
  'MSSQL': { icon: FaDatabase, color: 'text-red-600', category: 'database' },
  'CosmosDB': { icon: FaDatabase, color: 'text-purple-600', category: 'database' },
  'NoSQL(ElasticSearch)': { icon: SiElasticsearch, color: 'text-yellow-500', category: 'database' },
  '.NET Framework & .Net Core': { icon: SiDotnet, color: 'text-purple-600', category: 'framework' },
  'ADO.NET': { icon: FaDatabase, color: 'text-blue-600', category: 'framework' },
  'ADO': { icon: FaDatabase, color: 'text-blue-600', category: 'framework' },
  'OLEDB': { icon: FaDatabase, color: 'text-gray-600', category: 'framework' },
  'ODBC': { icon: FaDatabase, color: 'text-gray-600', category: 'framework' },
  'Dapper': { icon: FaDatabase, color: 'text-green-600', category: 'framework' },
  'HTML': { icon: SiHtml5, color: 'text-orange-500', category: 'language' },
  'CSS': { icon: SiCss3, color: 'text-blue-500', category: 'language' },
  'TFS': { icon: SiGit, color: 'text-blue-600', category: 'tool' },
  'SVN': { icon: SiSubversion, color: 'text-blue-500', category: 'tool' },
  'JIRA': { icon: SiJira, color: 'text-blue-500', category: 'tool' },
  'Crystal Reports': { icon: FaChartBar, color: 'text-blue-600', category: 'tool' },
  'Fast Report': { icon: FaChartBar, color: 'text-green-600', category: 'tool' },
  'Azure DevOps': { icon: SiGit, color: 'text-blue-600', category: 'tool' },
  'Azure Repos': { icon: SiGit, color: 'text-blue-600', category: 'tool' },
  'Azure Boards': { icon: SiAsana, color: 'text-blue-600', category: 'tool' },
};

// Helper function to get icon by name
export const getTechnologyIcon = (name: string): TechnologyIcon | null => {
  return technologyIcons[name] || null;
};

// Helper function to get icons by category
export const getIconsByCategory = (category: TechnologyIcon['category']): { [key: string]: TechnologyIcon } => {
  return Object.entries(technologyIcons)
    .filter(([_, icon]) => icon.category === category)
    .reduce((acc, [name, icon]) => ({ ...acc, [name]: icon }), {});
};

// Component for rendering technology icons with consistent styling
export interface TechnologyIconProps {
  name: string;
  size?: number;
  className?: string;
  showTooltip?: boolean;
}

export const TechnologyIconComponent: React.FC<TechnologyIconProps> = ({
  name,
  size = 24,
  className = '',
  showTooltip = false
}) => {
  const iconData = getTechnologyIcon(name);
  
  if (!iconData) {
    return null;
  }
  
  const IconComponent = iconData.icon;
  
  return (
    <IconComponent
      size={size}
      className={`${iconData.color} ${className}`}
      title={showTooltip ? name : undefined}
    />
  );
};

export default technologyIcons;
