import { getTechnologyIcon } from '../icons/TechnologyIcons';

export interface SkillCategory {
  title: string;
  skills: string[];
}

export interface SkillsData {
  categories: SkillCategory[];
  professionalSummary: string;
  currentFocusAreas: string[];
}

// Consolidated skills data - single source of truth
export const skillsData: SkillsData = {
  categories: [
    {
      title: "Programming Languages & Frameworks",
      skills: [
        "C#",
        "VB.NET",
        "ASP.NET",
        "T-SQL",
        "Razor",
        "LINQ",
        "Node.js",
        ".NET Framework & Core",
        "JavaScript",
        "TypeScript",
        "Web API",
      ],
    },
    {
      title: "Frontend Technologies",
      skills: ["React", "Angular", "jQuery", "HTML", "CSS"],
    },
    {
      title: "Databases & Data Access",
      skills: [
        "MSSQL",
        "MySQL",
        "PostgreSQL",
        "SQLite",
        "CosmosDB",
        "mongoDB",
        "NoSQL(ElasticSearch)",
        "ADO.NET",
        "ADO",
        "OLEDB",
        "ODBC",
        "Dapper",
        "Vector DBs"
      ],
    },
    {
      title: "Development Tools",
      skills: [
        "Visual Studio",
        "VS Code",
        "Crystal Reports",
        "Fast Report",
        "DevExpress",
        "Postman",
        "Docker",
        "Azure DevOps"
      ],
    },
    {
      title: "Data & Analytics",
      skills: ["Metabase", "JSON", "XML", "Web API"],
    },
    {
      title: "Version Control",
      skills: ["TFS", "SVN", "GitHub", "GitLab", "Azure Repos", "Bitbucket"],
    },
    {
      title: "Project Management",
      skills: [
        "Asana",
        "Azure Boards",
        "JIRA",
        "Notion",
        "Trello",
        "Confluence"
      ],
    },
  ],
  professionalSummary: "Experienced software developer with expertise in .NET technologies, full-stack web development, and database management. Proficient in modern frameworks and development tools.",
  currentFocusAreas: [
    "Cloud-native application development",
    "Microservices architecture with .NET",
    "Modern frontend frameworks (React, Angular)",
    "DevOps and containerization with Docker",
    "Database optimization and performance tuning",
  ],
};

// Utility function to get skill with icon data
export const getSkillWithIcon = (skillName: string) => {
  const iconData = getTechnologyIcon(skillName);
  return {
    name: skillName,
    icon: iconData?.icon,
    color: iconData?.color,
  };
};

// Transform data for modern theme format
export const getModernThemeSkillsData = () => {
  const modernMapping: { [key: string]: keyof ModernSkillsFormat } = {
    "Programming Languages & Frameworks": "languages",
    "Frontend Technologies": "technologies",
    "Databases & Data Access": "databases",
    "Development Tools": "tools",
    "Data & Analytics": "technologies",
    "Version Control": "version_control",
    "Project Management": "project_management",
  };

  const result: ModernSkillsFormat = {
    languages: [],
    databases: [],
    tools: [],
    technologies: [],
    version_control: [],
    project_management: [],
  };

  skillsData.categories.forEach(category => {
    const targetKey = modernMapping[category.title];
    if (targetKey && result[targetKey]) {
      result[targetKey].push(...category.skills);
    }
  });

  return result;
};

export interface ModernSkillsFormat {
  languages: string[];
  databases: string[];
  tools: string[];
  technologies: string[];
  version_control: string[];
  project_management: string[];
}

export default skillsData;
