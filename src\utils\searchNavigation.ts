import { SearchItem } from '../lib/searchIndex';

export const handleSearchNavigation = (
  item: SearchItem,
  onOpenWindow?: (windowId: string, windowName: string) => void
) => {
  if (item.windowId && item.windowName && onOpenWindow) {
    onOpenWindow(item.windowId, item.windowName);
    return;
  }

  if (item.action) {
    item.action();
    return;
  }

  // For items with path but no specific window, do nothing
  // This removes the popup trivia dialog
};

export const createSearchNavigationHandlers = (
  onOpenWindow?: (windowId: string, windowName: string) => void
) => {
  return {
    handleTechnologyItem: (_item: SearchItem) => {
      if (onOpenWindow) {
        onOpenWindow('my_computer', 'My Computer');
      }
    },

    handleSkillItem: (_item: SearchItem) => {
      if (onOpenWindow) {
        onOpenWindow('skills', 'Skills & Experience');
      }
    },

    handleContactItem: (_item: SearchItem) => {
      if (onOpenWindow) {
        onOpenWindow('contact', 'Contact Info');
      }
    },

    handleProjectItem: (item: SearchItem) => {
      if (item.windowId && item.windowName && onOpenWindow) {
        onOpenWindow(item.windowId, item.windowName);
      } else if (onOpenWindow) {
        onOpenWindow('projects', 'My Projects');
      }
    },

    handleNavigationItem: (item: SearchItem) => {
      if (item.windowId && item.windowName && onOpenWindow) {
        onOpenWindow(item.windowId, item.windowName);
      }
    },

    handleDocumentItem: (item: SearchItem) => {
      if (item.windowId && item.windowName && onOpenWindow) {
        onOpenWindow(item.windowId, item.windowName);
      }
    }
  };
};

export const smartSearchNavigation = (
  item: SearchItem,
  onOpenWindow?: (windowId: string, windowName: string) => void
) => {
  const handlers = createSearchNavigationHandlers(onOpenWindow);

  switch (item.category) {
    case 'Technology':
      handlers.handleTechnologyItem(item);
      break;
    case 'Skills':
      handlers.handleSkillItem(item);
      break;
    case 'Contact':
      handlers.handleContactItem(item);
      break;
    case 'Projects':
      handlers.handleProjectItem(item);
      break;
    case 'Navigation':
      handlers.handleNavigationItem(item);
      break;
    case 'Documents':
      handlers.handleDocumentItem(item);
      break;
    default:
      handleSearchNavigation(item, onOpenWindow);
      break;
  }
};

export const getSearchSuggestions = (query: string): string[] => {
  const commonSearches = [
    'react',
    'projects',
    'contact',
    'skills',
    'resume',
    'typescript',
    'javascript',
    'c#',
    'angular',
    'database',
    'email',
    'linkedin',
    'github',
    'certifications',
    'about me',
    'portfolio',
    'npm',
    'command runner',
    'vscode extension',
    'mobile app',
    'android',
    'ios'
  ];

  if (!query.trim()) return commonSearches.slice(0, 5);

  return commonSearches
    .filter(search => search.toLowerCase().includes(query.toLowerCase()))
    .slice(0, 5);
};
