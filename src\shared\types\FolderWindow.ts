import React from 'react';

/**
 * Represents a folder item (file or folder) in the folder window
 */
export interface FolderItem {
  id: string;
  name: string;
  icon: React.ComponentType<any> | React.ReactNode;
  type: 'folder' | 'file' | 'image' | 'custom';
  description?: string;
  size?: string;
  version?: string;
  color?: string;
  onClick?: () => void;
  onDoubleClick?: () => void;
  metadata?: Record<string, any>;
}

/**
 * Represents a sidebar navigation button
 */
export interface SidebarButton {
  id: string;
  label: string;
  icon?: string;
  onClick: () => void;
}

/**
 * Represents a sidebar section with buttons
 */
export interface SidebarSection {
  title: string;
  buttons: SidebarButton[];
}

/**
 * Configuration for the folder window sidebar
 */
export interface SidebarConfig {
  sections: SidebarSection[];
  details?: {
    title: string;
    description: string;
  };
  className?: string;
  width?: string;
}

/**
 * Layout types for folder content display
 */
export type FolderLayout = 'grid' | 'list' | 'custom';

/**
 * Content renderer function for custom layouts
 */
export type ContentRenderer = (items: FolderItem[]) => React.ReactNode;

/**
 * Props for the generic FolderWindow component
 */
export interface FolderWindowProps {
  // Header configuration
  title: string;
  icon: React.ReactNode;
  subtitle?: string;
  
  // Content configuration
  items?: FolderItem[];
  layout?: FolderLayout;
  customContent?: React.ReactNode;
  contentRenderer?: ContentRenderer;
  
  // Sidebar configuration
  sidebarConfig?: SidebarConfig;
  showSidebar?: boolean;
  
  // Event handlers
  onItemClick?: (item: FolderItem) => void;
  onItemDoubleClick?: (item: FolderItem) => void;
  onOpenWindow?: (id: string, name: string) => void;
  
  // WindowsFolderHeader props
  showNavigationButtons?: boolean;
  showToolbar?: boolean;
  showAddressBar?: boolean;
  onBackClick?: () => void;
  onForwardClick?: () => void;
  onUpClick?: () => void;
  onSearchClick?: () => void;
  onFoldersClick?: () => void;
  customActions?: React.ReactNode;
  variant?: 'default' | 'control-panel' | 'system';
  
  // Styling
  className?: string;
  contentClassName?: string;
  gridCols?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
}

/**
 * Default grid column configurations for different screen sizes
 */
export const DEFAULT_GRID_COLS = {
  documents: {
    sm: 3,
    md: 4,
    lg: 5,
    xl: 6
  },
  pictures: {
    sm: 2,
    md: 3,
    lg: 4,
    xl: 5
  },
  default: {
    sm: 2,
    md: 3,
    lg: 4,
    xl: 4
  }
} as const;
