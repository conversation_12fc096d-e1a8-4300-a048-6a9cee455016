
import { useState, useEffect, useCallback } from 'react';

export interface WindowData {
  position: { x: number; y: number };
  size: { width: number; height: number };
  isMaximized?: boolean;
}

export interface UseWindowDragProps {
  windowData: WindowData;
  onUpdatePosition: (position: { x: number; y: number }) => void;
  onBringToFront: () => void;
  isMobile?: boolean;
}

export const useWindowDrag = ({ windowData, onUpdatePosition, onBringToFront, isMobile }: UseWindowDragProps) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  const { position, isMaximized } = windowData;

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if ((e.target as HTMLElement).closest('.window-controls') || (e.target as HTMLElement).closest('.resize-handle')) {
      return;
    }
    if (isMaximized || isMobile) return;
    
    setIsDragging(true);
    setDragOffset({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
    onBringToFront();
  }, [isMaximized, isMobile, position.x, position.y, onBringToFront]);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const newPosition = {
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y
      };

      newPosition.x = Math.max(0, Math.min(newPosition.x, globalThis.window.innerWidth - windowData.size.width));
      newPosition.y = Math.max(0, Math.min(newPosition.y, globalThis.window.innerHeight - windowData.size.height - 40));

      onUpdatePosition(newPosition);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragOffset, windowData.size, onUpdatePosition]);

  return { handleMouseDown };
};
