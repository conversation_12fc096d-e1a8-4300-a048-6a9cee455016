
import React from 'react';

const StartMenuItem = ({
  icon,
  name,
  subtext,
  bold,
  onClick,
}: {
  icon: React.ReactNode;
  name: string;
  subtext?: string;
  bold?: boolean;
  onClick?: () => void;
}) => (
  <button
    className="w-full flex items-center space-x-3 px-3 py-1.5 hover:bg-[#316AC5] hover:text-white text-left text-sm rounded-sm transition-colors duration-150 group"
    onClick={onClick}
  >
    <div className="w-8 h-8 flex items-center justify-center">{icon}</div>
    <div className="flex-1">
      <span className={`text-black group-hover:text-white ${bold ? 'font-bold' : ''}`}>{name}</span>
      {subtext && <div className="text-xs text-gray-500 group-hover:text-gray-200">{subtext}</div>}
    </div>
  </button>
);

export default StartMenuItem;
