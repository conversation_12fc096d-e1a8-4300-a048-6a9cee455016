import { iconMap } from "../../lib/icon-map";
import WindowsFolderHeader from "./WindowsFolderHeader";
import { skillsData } from "../../shared/data/skillsData";

const SkillsContent = () => {

  return (
    <div className="bg-white font-tahoma">
      <WindowsFolderHeader
        title="Skills & Experience"
      />
      <div className="p-4">
        <div className="bg-blue-50 p-3 rounded mb-4 border-l-4 border-blue-500">
          <p className="text-sm text-blue-800">
            <strong>Professional Summary:</strong> {skillsData.professionalSummary}
          </p>
        </div>

        {skillsData.categories.map((category) => (
          <div key={category.title} className="mb-6">
            <h3 className="font-bold text-blue-800 mb-3 pb-1 border-b border-gray-300 bg-gradient-to-r from-blue-100 to-transparent px-2 py-1">
              {category.title}
            </h3>
            <div className="grid grid-cols-2 gap-2">
              {category.skills.map((skill) => {
                const skillData = iconMap[skill];
                const IconComponent = skillData?.icon;

                return (
                  <div
                    key={skill}
                    className="flex items-center space-x-2 p-2 bg-gray-50 rounded border hover:bg-blue-50 transition-colors"
                  >
                    {IconComponent ? (
                      <IconComponent className={`w-4 h-4 ${skillData.color}`} />
                    ) : (
                      <span className="text-lg">🔧</span>
                    )}
                    <span className="text-sm font-medium">{skill}</span>
                  </div>
                );
              })}
            </div>
          </div>
        ))}

        <div className="mt-6 p-4 bg-gray-100 rounded border">
          <h3 className="font-bold text-gray-800 mb-2">Current Focus Areas</h3>
          <ul className="text-sm space-y-1 text-gray-700">
            {skillsData.currentFocusAreas.map((area, index) => (
              <li key={index}>• {area}</li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default SkillsContent;
