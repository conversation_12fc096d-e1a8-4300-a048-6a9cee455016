
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar.tsx';
import { Button } from '@/components/ui/button';
import { Mail, Phone, Github, Linkedin, Copy, FileText } from 'lucide-react';
import CVModal from './CVModal';
import profilePicture from '../assets/img/dp.png';
import { useScrollReveal } from '../hooks/useScrollReveal';

interface ProfileSectionProps {
  profileData: {
    name: string;
    title: string;
    bio: string;
    email: string;
    phone: string;
    location: string;
    github: string;
    linkedin: string;
    cvUrl?: string;
  };
  activeContact: string | null;
  onContactClick: (contactType: string) => void;
}

const ProfileSection = React.forwardRef<HTMLDivElement, ProfileSectionProps>(
  ({ profileData, activeContact, onContactClick }, ref) => {
    const [showCVModal, setShowCVModal] = useState(false);

    // Scroll reveal hooks for individual elements with very gradual cascade timing
    const avatarRef = useScrollReveal({ animationClass: 'animate-scale-in', delay: 200 });
    const nameRef = useScrollReveal({ animationClass: 'animate-fade-in-up', delay: 600 });
    const titleRef = useScrollReveal({ animationClass: 'animate-fade-in-up', delay: 1000 });
    const contactIconsRef = useScrollReveal({ animationClass: 'animate-fade-in-up', delay: 1400 });
    const bioRef = useScrollReveal({ animationClass: 'animate-fade-in-up', delay: 1800 });

    const copyToClipboard = (text: string) => {
      navigator.clipboard.writeText(text);
    };

    const handleLinkClick = (url: string) => {
      window.open(url, '_blank');
    };

    return (
      <section ref={ref} data-section="profile" className="scroll-mt-28 md:scroll-mt-32">
        <Card className="overflow-hidden border-0 bg-white/80 dark:bg-white/5 backdrop-blur-xl rounded-3xl shadow-lg dark:shadow-2xl dark:shadow-black/20 ring-1 ring-gray-200/50 dark:ring-white/10">
          <CardContent className="p-8 md:p-12">
            <div className="flex flex-col items-center text-center gap-8 md:gap-12 md:flex-row md:items-start md:text-left">
              <div ref={avatarRef} className="flex flex-col items-center gap-6 relative">
                {/* Animated Ring with Gray/Black Gradient */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-gray-400 to-black animate-spin opacity-20" style={{ animationDuration: '8s' }} />

                <Avatar className="w-32 h-32 md:w-40 md:h-40 shadow-2xl shadow-blue-500/25 ring-4 ring-white/20 relative z-10">
                  <AvatarImage
                    src={profilePicture}
                    alt={`${profileData.name} - Profile Picture`}
                    className="object-cover"
                  />
                  <AvatarFallback className="text-2xl md:text-3xl font-light bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                    {profileData.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                
                <Button
                  onClick={() => setShowCVModal(true)}
                  className="group relative px-6 py-3 bg-gradient-to-r from-blue-500 to-white dark:from-gray-600 dark:to-black hover:from-blue-600 hover:to-gray-100 dark:hover:from-gray-700 dark:hover:to-gray-900 text-white dark:text-white font-medium rounded-full shadow-lg shadow-blue-500/25 dark:shadow-gray-500/25 hover:shadow-xl hover:shadow-blue-500/40 dark:hover:shadow-gray-500/40 transition-all duration-300 overflow-hidden"
                >
                  <span className="relative z-10 flex items-center gap-2">
                    <FileText className="w-4 h-4" />
                    View CV
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-gray-200 dark:from-gray-500 dark:to-gray-800 opacity-0 group-hover:opacity-20 transition-opacity duration-300" />
                </Button>
              </div>
              
              <div className="flex-1">
                <h3 ref={nameRef} className="text-3xl md:text-4xl font-light text-gray-900 dark:text-white mb-3 tracking-tight">
                  {profileData.name}
                </h3>
                <p ref={titleRef} className="text-xl md:text-2xl text-blue-600 dark:text-blue-400 mb-6 md:mb-8 font-light" style={{ color: '#4169E1' }}>
                  {profileData.title}
                </p>
                
                {/* Enhanced Contact Icons - Removed location */}
                <div ref={contactIconsRef} className="flex items-center justify-center md:justify-start gap-4 mb-6 md:mb-8">
                  {[
                    { type: 'email', icon: Mail, color: 'blue' },
                    { type: 'phone', icon: Phone, color: 'green' },
                    { type: 'github', icon: Github, color: 'gray' },
                    { type: 'linkedin', icon: Linkedin, color: 'blue' }
                  ].map(({ type, icon: Icon, color }) => (
                    <button
                      key={type}
                      onClick={() => onContactClick(type)}
                      className={`group relative w-12 h-12 rounded-xl transition-all duration-300 ${
                        activeContact === type 
                          ? `bg-${color}-500 text-white shadow-lg shadow-${color}-500/40 scale-110` 
                          : `bg-gray-200/50 dark:bg-white/10 text-gray-700 dark:text-white hover:bg-gray-200/80 dark:hover:bg-white/20 backdrop-blur-sm`
                      }`}
                    >
                      <Icon className="w-5 h-5 mx-auto" />
                      
                      {/* Glow effect */}
                      {activeContact === type && (
                        <div className={`absolute inset-0 rounded-xl bg-${color}-500 opacity-30 blur-lg scale-150`} />
                      )}
                    </button>
                  ))}
                </div>

                {/* Enhanced Active Contact Display - Removed location handling */}
                {activeContact && (
                  <div className="mb-6 md:mb-8 animate-fade-in">
                    <div className="inline-flex items-center gap-4 p-4 rounded-xl bg-gray-100/80 dark:bg-white/10 backdrop-blur-sm border border-gray-200 dark:border-white/20 shadow-lg">
                      <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                        activeContact === 'email' ? 'bg-blue-500 shadow-blue-500/40' :
                        activeContact === 'phone' ? 'bg-green-500 shadow-green-500/40' :
                        activeContact === 'github' ? 'bg-gray-500 shadow-gray-500/40' :
                        activeContact === 'linkedin' ? 'bg-blue-500 shadow-blue-500/40' : 'bg-gray-500'
                      } shadow-lg`}>
                        {activeContact === 'email' && <Mail className="w-5 h-5 text-white" />}
                        {activeContact === 'phone' && <Phone className="w-5 h-5 text-white" />}
                        {activeContact === 'github' && <Github className="w-5 h-5 text-white" />}
                        {activeContact === 'linkedin' && <Linkedin className="w-5 h-5 text-white" />}
                      </div>
                      
                      {(activeContact === 'github' || activeContact === 'linkedin') ? (
                        <button
                          onClick={() => handleLinkClick(
                            activeContact === 'github' ? profileData.github : profileData.linkedin
                          )}
                          className="text-blue-500 dark:text-blue-300 hover:text-blue-600 dark:hover:text-blue-200 underline font-medium text-sm md:text-base transition-colors"
                        >
                          {activeContact === 'github' && profileData.github}
                          {activeContact === 'linkedin' && profileData.linkedin}
                        </button>
                      ) : (
                        <span className="text-gray-800 dark:text-white font-medium text-sm md:text-base">
                          {activeContact === 'email' && profileData.email}
                          {activeContact === 'phone' && profileData.phone}
                        </span>
                      )}
                      
                      <button
                        onClick={() => copyToClipboard(
                          activeContact === 'email' ? profileData.email :
                          activeContact === 'phone' ? profileData.phone :
                          activeContact === 'github' ? profileData.github :
                          activeContact === 'linkedin' ? profileData.linkedin : ''
                        )}
                        className="w-8 h-8 rounded-lg bg-black/5 dark:bg-white/20 hover:bg-black/10 dark:hover:bg-white/30 flex items-center justify-center transition-all duration-200 group"
                      >
                        <Copy className="w-4 h-4 text-gray-700 dark:text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </div>
                  </div>
                )}
                
                <p ref={bioRef} className="text-gray-700 dark:text-blue-100/80 leading-relaxed mb-6 md:mb-8 text-sm md:text-base font-light max-w-2xl">
                  {profileData.bio}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <CVModal
          open={showCVModal}
          onOpenChange={setShowCVModal}
          cvUrl={profileData.cvUrl}
        />
      </section>
    );
  }
);

ProfileSection.displayName = 'ProfileSection';

export default ProfileSection;
