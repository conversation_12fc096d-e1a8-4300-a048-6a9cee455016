
import { useEffect } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Download, Loader2 } from 'lucide-react';
import { usePdfIframeResize } from '@shared/hooks/usePdfIframeResize';
import { getEmbedUrl, getDownloadUrl, getPrimaryEmbedUrl, getPrimaryDownloadUrl } from '@shared/config/pdfConfig';

interface CVModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  cvUrl?: string; // Made optional since we now use shared configuration
}

const CVModal = ({ open, onOpenChange, cvUrl }: CVModalProps) => {
  // Use shared PDF iframe resize hook for better display
  const { iframeRef, handleIframeLoad, isLoading, resetResize } = usePdfIframeResize();

  // Reset the resize state when modal opens to prevent infinite loops
  useEffect(() => {
    if (open) {
      resetResize();
    }
  }, [open, resetResize]);

  // Always use the primary configured URL (consolidated "my resume" document)
  // This ensures consistency with the XP theme regardless of any passed cvUrl
  const embedUrl = getPrimaryEmbedUrl();
  const downloadUrl = getPrimaryDownloadUrl();

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = 'CV.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl w-full h-[90vh] max-h-[900px] p-0 gap-0 flex flex-col">
        {/* Header with download button - positioned to avoid overlap with close button */}
        <div className="flex justify-between items-center px-4 py-3 border-b border-gray-200 flex-shrink-0">
          <h2 className="text-lg font-semibold text-gray-900">Resume / CV</h2>
          <Button
            onClick={handleDownload}
            className="flex items-center gap-2 ml-8"
            variant="outline"
            size="sm"
          >
            <Download className="w-4 h-4" />
            Download CV
          </Button>
        </div>

        {/* PDF iframe container - no whitespace, full remaining height */}
        <div className="flex-1 bg-gray-50 overflow-hidden min-h-0 relative">
          {/* Loading indicator */}
          {isLoading && (
            <div className="absolute inset-0 bg-gray-50 flex items-center justify-center z-10">
              <div className="flex flex-col items-center gap-3">
                <Loader2 className="w-8 h-8 animate-spin text-gray-600" />
                <p className="text-sm text-gray-600">Loading PDF...</p>
              </div>
            </div>
          )}

          <iframe
            ref={iframeRef}
            src={embedUrl}
            className="w-full h-full border-0"
            title="CV Preview"
            allow="autoplay"
            onLoad={handleIframeLoad}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CVModal;
