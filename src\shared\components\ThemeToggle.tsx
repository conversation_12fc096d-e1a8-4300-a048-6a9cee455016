import React from 'react';
import { Switch } from '@/components/ui/switch';
import { <PERSON>, Moon, Computer } from 'lucide-react';
import { useUnifiedTheme } from '@shared/contexts/UnifiedThemeContext';
import { cn } from '@shared/utils';

export type ThemeToggleVariant = 'modern-dark-light' | 'xp-modern';
export type ThemeTogglePosition = 'fixed' | 'inline';

export interface ThemeToggleProps {
  variant: ThemeToggleVariant;
  position?: ThemeTogglePosition;
  className?: string;
  showLabels?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  variant,
  position = 'inline',
  className,
  showLabels = false,
  size = 'md'
}) => {
  const { 
    modernTheme, 
    toggleModernTheme, 
    toggleAppTheme, 
    isXPTheme, 
    isModernTheme 
  } = useUnifiedTheme();

  const sizeClasses = {
    sm: {
      container: 'p-2',
      icon: 'w-3 h-3',
      text: 'text-xs',
      gap: 'gap-2'
    },
    md: {
      container: 'p-3',
      icon: 'w-4 h-4',
      text: 'text-sm',
      gap: 'gap-3'
    },
    lg: {
      container: 'p-4',
      icon: 'w-5 h-5',
      text: 'text-base',
      gap: 'gap-4'
    }
  };

  const currentSize = sizeClasses[size];

  // Modern Dark/Light theme toggle
  if (variant === 'modern-dark-light') {
    const baseClasses = cn(
      'flex items-center bg-white/10 dark:bg-black/20 backdrop-blur-xl border border-white/20 dark:border-gray-700/30 rounded-2xl shadow-2xl transition-all duration-300',
      currentSize.container,
      currentSize.gap,
      position === 'fixed' && 'fixed top-[19px] left-6 z-50',
      className
    );

    return (
      <div className={baseClasses}>
        <Sun className={cn('text-yellow-500', currentSize.icon)} />
        <Switch
          checked={modernTheme === 'dark'}
          onCheckedChange={toggleModernTheme}
          className="data-[state=checked]:bg-gray-800 data-[state=unchecked]:bg-gray-200"
        />
        <Moon className={cn('text-blue-400', currentSize.icon)} />
        {showLabels && (
          <span className={cn('font-medium text-gray-700 dark:text-white/80', currentSize.text)}>
            {modernTheme === 'dark' ? 'Dark' : 'Light'}
          </span>
        )}
      </div>
    );
  }

  // XP/Modern theme toggle
  if (variant === 'xp-modern') {
    const baseClasses = cn(
      'group flex items-center font-medium transition-all duration-300',
      currentSize.gap,
      position === 'fixed' && 'fixed top-6 right-6 z-50 bg-white/90 dark:bg-black/90 backdrop-blur-xl border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg',
      position === 'inline' && 'px-4 py-3 rounded-xl text-slate-700 dark:text-white/80 hover:text-slate-900 dark:hover:text-white hover:bg-slate-200/50 dark:hover:bg-white/20 hover:scale-105',
      className
    );

    return (
      <button
        onClick={toggleAppTheme}
        className={baseClasses}
        title={isXPTheme ? "Switch to Modern Theme" : "Switch to Windows XP Theme"}
      >
        <Computer className={cn(
          'text-slate-500 dark:text-white/80 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-colors',
          currentSize.icon
        )} />
        {showLabels && (
          <span className={currentSize.text}>
            {isXPTheme ? 'Modern Theme' : 'XP Theme'}
          </span>
        )}
        {position === 'inline' && (
          <div className="w-0 group-hover:w-1 h-1 bg-blue-400 rounded-full transition-all duration-300" />
        )}
      </button>
    );
  }

  return null;
};

export default ThemeToggle;
