
import { File, FileCode, FileText, FileJson, FileTerminal, FileArchive, FileQuestion, LucideProps } from 'lucide-react';
import React from 'react';

const fileExtensionIconMap: { [key: string]: React.ElementType } = {
  js: FileCode,
  html: FileCode,
  py: FileCode,
  ts: FileCode,
  tsx: FileCode,
  css: FileCode,
  json: FileJson,
  txt: FileText,
  doc: FileText,
  docx: FileText,
  pdf: FileText,
  exe: FileTerminal,
  dll: FileArchive,
  jar: FileArchive,
  zip: FileArchive,
  '404': FileQuestion,
};

interface FileIconProps extends LucideProps {
  filename: string;
}

export const FileIcon = ({ filename, ...props }: FileIconProps) => {
  const extension = filename.split('.').pop()?.toLowerCase() || '';
  const IconComponent = fileExtensionIconMap[extension] || File;
  return <IconComponent {...props} />;
};
