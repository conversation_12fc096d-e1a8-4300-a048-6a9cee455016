import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '../ui/card';
import {
  <PERSON><PERSON>hart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Tooltip,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  LineChart,
  Line
} from 'recharts';
import { BarChart3, TrendingUp, Target, Rocket, Activity } from 'lucide-react';
import { xpSkillCategories, xpSkillsData, xpCareerMilestones, xpTechnologyEvolution, xpProjectImpacts } from '../../shared/data/xpAnalyticsData';
import EnhancedCareerTimeline from './EnhancedCareerTimeline';

/**
 * Modern Portfolio Analytics Window Content
 * Features modern card-based layout with professional data visualization
 */
const PortfolioAnalyticsContent: React.FC = () => {

  // Prepare data for charts
  const categoryDistribution = xpSkillsData.reduce((acc, skill) => {
    const existing = acc.find(item => item.category === skill.category);
    if (existing) {
      existing.count += 1;
      existing.avgProficiency = (existing.avgProficiency + skill.proficiency) / 2;
    } else {
      acc.push({
        category: skill.category,
        count: 1,
        avgProficiency: skill.proficiency,
        color: getColorForCategory(skill.category)
      });
    }
    return acc;
  }, [] as Array<{ category: string, count: number, avgProficiency: number, color: string }>);

  function getColorForCategory(category: string): string {
    const colorMap: { [key: string]: string } = {
      'backend': '#3B82F6',
      'frontend': '#8B5CF6',
      'database': '#F59E0B',
      'tools': '#10B981'
    };
    return colorMap[category] || '#6B7280';
  }

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || payload.length === 0) return null;

    return (
      <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
        {label && <p className="font-medium text-gray-900 dark:text-white mb-1">{label}</p>}
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {entry.value}
          </p>
        ))}
      </div>
    );
  };

  return (
    <div className="h-full bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-black overflow-auto">
      {/* Modern Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl">
        <div className="flex items-center gap-3 mb-2">
          <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center shadow-lg">
            <BarChart3 className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-light text-gray-900 dark:text-white tracking-tight">
              Portfolio Analytics
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Professional insights and data visualization
            </p>
          </div>
        </div>
      </div>

      {/* Main Content - Modern Card Layout */}
      <div className="p-6 space-y-8">
        {/* Skills Overview Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Skills Radar Chart */}
          <Card className="border-0 bg-white/80 dark:bg-white/5 backdrop-blur-xl rounded-2xl shadow-lg dark:shadow-xl ring-1 ring-gray-200/50 dark:ring-white/10 hover:ring-gray-300 dark:hover:ring-white/20 transition-all duration-500">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl font-light text-gray-900 dark:text-white">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-600 to-blue-800 flex items-center justify-center shadow-lg">
                  <Target className="w-5 h-5 text-white" />
                </div>
                Skills Proficiency
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <RadarChart data={xpSkillCategories}>
                    <PolarGrid stroke="#E5E7EB" />
                    <PolarAngleAxis
                      dataKey="category"
                      tick={{ fontSize: 12, fill: '#6B7280' }}
                    />
                    <PolarRadiusAxis
                      angle={90}
                      domain={[0, 10]}
                      tick={{ fontSize: 10, fill: '#6B7280' }}
                    />
                    <Radar
                      name="Proficiency"
                      dataKey="value"
                      stroke="#3B82F6"
                      fill="#3B82F6"
                      fillOpacity={0.2}
                      strokeWidth={2}
                    />
                    <Tooltip content={<CustomTooltip />} />
                  </RadarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Technology Distribution */}
          <Card className="border-0 bg-white/80 dark:bg-white/5 backdrop-blur-xl rounded-2xl shadow-lg dark:shadow-xl ring-1 ring-gray-200/50 dark:ring-white/10 hover:ring-gray-300 dark:hover:ring-white/20 transition-all duration-500">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl font-light text-gray-900 dark:text-white">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-purple-600 to-purple-800 flex items-center justify-center shadow-lg">
                  <BarChart3 className="w-5 h-5 text-white" />
                </div>
                Technology Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={categoryDistribution}
                      cx="50%"
                      cy="50%"
                      outerRadius={100}
                      innerRadius={40}
                      paddingAngle={2}
                      dataKey="count"
                    >
                      {categoryDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomTooltip />} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Career Timeline Section */}
        <Card className="border-0 bg-white/80 dark:bg-white/5 backdrop-blur-xl rounded-2xl shadow-lg dark:shadow-xl ring-1 ring-gray-200/50 dark:ring-white/10 hover:ring-gray-300 dark:hover:ring-white/20 transition-all duration-500">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-xl font-light text-gray-900 dark:text-white">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-green-600 to-green-800 flex items-center justify-center shadow-lg">
                <Activity className="w-5 h-5 text-white" />
              </div>
              Enhanced Career Timeline
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <EnhancedCareerTimeline />
          </CardContent>
        </Card>

        {/* Technology Evolution & Project Impact */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Technology Evolution */}
          <Card className="border-0 bg-white/80 dark:bg-white/5 backdrop-blur-xl rounded-2xl shadow-lg dark:shadow-xl ring-1 ring-gray-200/50 dark:ring-white/10 hover:ring-gray-300 dark:hover:ring-white/20 transition-all duration-500">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl font-light text-gray-900 dark:text-white">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-orange-600 to-orange-800 flex items-center justify-center shadow-lg">
                  <TrendingUp className="w-5 h-5 text-white" />
                </div>
                Technology Evolution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={xpTechnologyEvolution}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                    <XAxis dataKey="year" tick={{ fontSize: 12, fill: '#6B7280' }} />
                    <YAxis domain={[0, 10]} tick={{ fontSize: 12, fill: '#6B7280' }} />
                    <Tooltip content={<CustomTooltip />} />
                    <Line
                      type="monotone"
                      dataKey="level"
                      stroke="#F59E0B"
                      strokeWidth={3}
                      dot={{ fill: '#F59E0B', strokeWidth: 2, r: 6 }}
                      activeDot={{ r: 8, stroke: '#F59E0B', strokeWidth: 2 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Project Impact */}
          <Card className="border-0 bg-white/80 dark:bg-white/5 backdrop-blur-xl rounded-2xl shadow-lg dark:shadow-xl ring-1 ring-gray-200/50 dark:ring-white/10 hover:ring-gray-300 dark:hover:ring-white/20 transition-all duration-500">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl font-light text-gray-900 dark:text-white">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-red-600 to-red-800 flex items-center justify-center shadow-lg">
                  <Rocket className="w-5 h-5 text-white" />
                </div>
                Project Impact
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {xpProjectImpacts.map((project, index) => (
                  <div key={index} className="p-4 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-700/50 hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 transition-all duration-300">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold text-gray-900 dark:text-white">{project.project}</h3>
                      <span className="text-xs text-gray-600 dark:text-gray-400 bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded-full">
                        {project.category}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-3 mb-3">
                      {Object.entries(project.metrics).map(([key, value]) => (
                        <div key={key} className="flex justify-between items-center">
                          <span className="text-sm text-gray-700 dark:text-gray-300 capitalize">
                            {key.replace(/([A-Z])/g, ' $1').trim()}:
                          </span>
                          <div className="flex items-center gap-2">
                            <div className="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                              <div
                                className="h-2 rounded-full transition-all duration-500"
                                style={{
                                  width: `${value}%`,
                                  backgroundColor: project.xpColor
                                }}
                              />
                            </div>
                            <span className="text-sm font-medium text-gray-800 dark:text-gray-200 w-8">
                              {value}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="px-2 py-1 text-xs text-white rounded-md shadow-sm"
                          style={{ backgroundColor: project.xpColor }}
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PortfolioAnalyticsContent;
