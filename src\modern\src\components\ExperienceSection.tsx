
import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, MapPin } from 'lucide-react';
import { useScrollReveal, useStaggeredScrollReveal } from '../hooks/useScrollReveal';

interface WorkExperience {
  id: number;
  position: string;
  company: string;
  duration: string;
  achievements: string[];
}

interface ExperienceSectionProps {
  workExperiences: WorkExperience[];
}

const ExperienceSection = React.forwardRef<HTMLDivElement, ExperienceSectionProps>(
  ({ workExperiences }, ref) => {
    // Scroll reveal hooks with very elegant timeline effect
    const titleRef = useScrollReveal({ animationClass: 'animate-fade-in-up', delay: 200 });
    const experienceListRef = useStaggeredScrollReveal(workExperiences.length, {
      animationClass: 'animate-slide-in-left',
      delay: 600,
      staggerDelay: 500
    });

    return (
      <section ref={ref} data-section="experience" className="scroll-mt-28 md:scroll-mt-32">
        <div ref={titleRef} className="text-center mb-6">
          <h2 className="text-2xl md:text-3xl font-light text-gray-800 dark:text-white mb-2 tracking-tight">
            Work Experience
          </h2>
          <div className="w-16 h-1 bg-gradient-to-r from-gray-500 to-gray-800 dark:from-white dark:to-gray-400 mx-auto rounded-full mb-2" />
          <p className="text-gray-700 dark:text-gray-300 text-base max-w-2xl mx-auto">
            Professional journey and key achievements
          </p>
        </div>
        
        {/* Timeline Container */}
        <div className="relative max-w-4xl mx-auto">
          {/* Vertical Line - Made smaller */}
          <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gradient-to-b from-gray-500 via-gray-700 to-gray-900 dark:from-white dark:via-gray-300 dark:to-gray-600"></div>
          
          <div ref={experienceListRef} className="space-y-4">
            {workExperiences.map((job, index) => (
              <div key={job.id} className="relative flex items-start gap-6">
                {/* Checkpoint - Moved 15px to the right for proper centering */}
                <div className="relative z-10 flex-shrink-0" style={{ marginLeft: '15px' }}>
                  <div className="w-6 h-6 rounded-full bg-gradient-to-r from-gray-500 to-gray-800 dark:from-white dark:to-gray-600 shadow-lg"></div>
                </div>
                
                {/* Content Card */}
                <div className="flex-1 pb-8">
                  <Card className="border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                    <CardHeader className="pb-3">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                        <div className="flex-1">
                          <CardTitle className="text-lg font-semibold text-gray-800 dark:text-white mb-1">
                            {job.position}
                          </CardTitle>
                          <CardDescription className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                            <MapPin className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                            {job.company}
                          </CardDescription>
                        </div>
                        <Badge className="flex items-center gap-1 px-3 py-1 bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-200 border-gray-200 dark:border-gray-600 rounded-full text-xs">
                          <Calendar className="w-3 h-3" />
                          {job.duration}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <ul className="space-y-2">
                        {job.achievements.slice(0, 3).map((achievement, achievementIndex) => (
                          <li key={achievementIndex} className="flex items-start gap-2 text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                            <div className="w-1.5 h-1.5 rounded-full bg-gray-500 dark:bg-gray-400 mt-2 flex-shrink-0"></div>
                            <span>{achievement}</span>
                          </li>
                        ))}
                        {job.achievements.length > 3 && (
                          <li className="text-xs text-gray-600 dark:text-gray-400 italic">
                            +{job.achievements.length - 3} more achievements
                          </li>
                        )}
                      </ul>
                    </CardContent>
                  </Card>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }
);

ExperienceSection.displayName = 'ExperienceSection';

export default ExperienceSection;
