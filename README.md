# Windows XP Portfolio

A nostalgic Windows XP-themed portfolio website built with modern web technologies.

## Getting Started

### Prerequisites

- Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

### Installation

```sh
# Clone the repository
git clone <YOUR_GIT_URL>

# Navigate to the project directory
cd <YOUR_PROJECT_NAME>

# Install dependencies
npm install

# Start the development server
npm run dev
```

## Technologies Used

This project is built with:

- **Vite** - Fast build tool and development server
- **TypeScript** - Type-safe JavaScript
- **React** - UI library
- **shadcn/ui** - Modern UI components
- **Tailwind CSS** - Utility-first CSS framework

## Features

### 🔍 **Comprehensive Search System**
- **Windows XP-style search dialog** accessible from the Start menu
- **Fuzzy search algorithm** with intelligent scoring and ranking
- **Category filtering** (Navigation, Projects, Skills, Technology, Contact, Documents)
- **Smart navigation** that opens relevant windows/dialogs automatically
- **Session-based search history** (no persistent storage)
- **Keyboard navigation** with arrow keys, Enter, and Escape
- **Real-time results** as you type

### 🖥️ **Authentic Windows XP Experience**
- **Pixel-perfect Windows XP styling** with authentic colors and fonts
- **Functional window management** with drag, resize, minimize, and maximize
- **Start menu and taskbar** with proper hover effects and animations
- **Dialog system** with single-instance protection and proper z-index management
- **Desktop icons** with double-click functionality
- **System sounds** and visual feedback

### 📱 **Modern Web Technologies**
- **Responsive design** that works on desktop and mobile
- **SEO optimized** for search engines
- **Fast loading** with Vite build system
- **Type-safe** with TypeScript
- **Component-based** architecture with React 18

## Development

### Local Development

Run the development server:

```sh
npm run dev
```

The application will be available at `http://localhost:8080`

### Building for Production

```sh
npm run build
```

### Preview Production Build

```sh
npm run preview
```

## Deployment

This project can be deployed to any static hosting service such as:

- Vercel
- Netlify
- GitHub Pages
- AWS S3 + CloudFront
- Any other static hosting provider

Simply build the project and upload the `dist` folder to your hosting service.
