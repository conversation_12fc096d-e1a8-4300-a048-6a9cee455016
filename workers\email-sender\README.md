# Portfolio Email Sender - <PERSON>flare Worker

This Cloudflare Worker handles contact form submissions from the Windows XP Portfolio website and sends emails via Resend API.

## Setup

1. **Install dependencies:**
   ```bash
   cd workers/email-sender
   npm install
   ```

2. **Configure Resend API Key:**
   ```bash
   # For production deployment
   wrangler secret put RESEND_API_KEY
   # Enter your Resend API key when prompted
   ```

3. **Local Development:**
   ```bash
   # Update .dev.vars with your Resend API key
   npm run dev
   ```

4. **Deploy to Cloudflare:**
   ```bash
   npm run deploy
   ```

## API Endpoint

**POST** `/api/send-email`

### Request Body:
```json
{
  "name": "<PERSON>",
  "fromEmail": "<EMAIL>", 
  "subject": "Contact Form Submission",
  "message": "Hello, I'd like to get in touch..."
}
```

### Response:
```json
{
  "success": true,
  "message": "Email sent successfully!",
  "emailId": "resend-email-id"
}
```

## Security Features

- API key is stored securely as a Cloudflare Worker secret
- CORS headers configured for cross-origin requests
- Input validation and sanitization
- Rate limiting handled on the frontend
- No sensitive data exposed to client-side code

## Domain Configuration

Update the routes in `wrangler.toml` to match your domain:
- Production: `markoverano.dev/api/send-email`
- Development: `*.markoverano.dev/api/send-email`
