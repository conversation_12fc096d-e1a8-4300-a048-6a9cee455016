import React from 'react';
import { ChevronLeft, ChevronRight, ChevronUp, Search, FolderOpen, MoreHorizontal } from 'lucide-react';
import { openSearchDialog } from '../../utils/dialogUtils';

export interface WindowsFolderHeaderProps {
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  showNavigationButtons?: boolean;
  showToolbar?: boolean;
  showAddressBar?: boolean;
  onBackClick?: () => void;
  onForwardClick?: () => void;
  onUpClick?: () => void;
  onSearchClick?: () => void;
  onFoldersClick?: () => void;
  customActions?: React.ReactNode;
  variant?: 'default' | 'control-panel' | 'system';
  onOpenWindow?: (windowId: string, windowName: string) => void;
}

const WindowsFolderHeader: React.FC<WindowsFolderHeaderProps> = ({
  title,
  subtitle,
  icon,
  showNavigationButtons = true,
  showToolbar = true,
  showAddressBar = true,
  onBackClick,
  onForwardClick,
  onUpClick,
  onSearchClick,
  onFoldersClick,
  customActions,
  variant = 'default',
  onOpenWindow
}) => {
  const renderIcon = () => {
    if (!icon) return null;
    if (typeof icon === 'string') {
      return <span>{icon}</span>;
    }
    return icon;
  };

  // Enhanced search handler that opens the search dialog
  const handleSearchClick = () => {
    if (onSearchClick) {
      // Use custom search handler if provided
      onSearchClick();
    } else {
      // Default behavior: open the search dialog
      openSearchDialog(onOpenWindow);
    }
  };

  // For non-folder variants, use the old styling
  if (variant === 'control-panel') {
    return (
      <div className="bg-white border-b border-gray-300 p-4 shadow-sm">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded mr-3 flex items-center justify-center shadow-md">
            {renderIcon()}
          </div>
          <div>
            <h1 className="text-2xl font-bold text-blue-800">{title}</h1>
            {subtitle && <p className="text-base text-gray-700 font-semibold">{subtitle}</p>}
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'system') {
    return (
      <div className="flex items-center mb-4 pb-3 border-b-2 border-blue-500 bg-white p-4">
        <div className="w-8 h-8 bg-blue-500 rounded mr-3 flex items-center justify-center">
          {renderIcon()}
        </div>
        <div>
          <h2 className="text-lg font-bold text-blue-800">{title}</h2>
          {subtitle && <p className="text-sm text-gray-600">{subtitle}</p>}
        </div>
      </div>
    );
  }

  // Default variant - Windows XP folder header
  return (
    <div className="bg-gradient-to-b from-gray-100 to-gray-200 border-b border-gray-300">
      {/* Toolbar */}
      {showToolbar && (
        <div className="flex items-center px-2 py-1 border-b border-gray-300 bg-gradient-to-b from-gray-50 to-gray-100">
          {/* Navigation buttons */}
          {showNavigationButtons && (
            <div className="flex items-center mr-2">
              <button
                onClick={onBackClick}
                disabled={!onBackClick}
                className="w-8 h-8 flex items-center justify-center border border-gray-400 bg-gradient-to-b from-white to-gray-100 hover:from-gray-50 hover:to-gray-150 disabled:opacity-50 disabled:cursor-not-allowed mr-1 rounded-sm"
                title="Back"
              >
                <ChevronLeft size={16} className="text-gray-700" />
              </button>
              <button
                onClick={onForwardClick}
                disabled={!onForwardClick}
                className="w-8 h-8 flex items-center justify-center border border-gray-400 bg-gradient-to-b from-white to-gray-100 hover:from-gray-50 hover:to-gray-150 disabled:opacity-50 disabled:cursor-not-allowed mr-1 rounded-sm"
                title="Forward"
              >
                <ChevronRight size={16} className="text-gray-700" />
              </button>
              <button
                onClick={onUpClick}
                disabled={!onUpClick}
                className="w-8 h-8 flex items-center justify-center border border-gray-400 bg-gradient-to-b from-white to-gray-100 hover:from-gray-50 hover:to-gray-150 disabled:opacity-50 disabled:cursor-not-allowed mr-3 rounded-sm"
                title="Up"
              >
                <ChevronUp size={16} className="text-gray-700" />
              </button>
            </div>
          )}

          {/* Separator */}
          <div className="w-px h-6 bg-gray-400 mr-3"></div>

          {/* Search button */}
          <button
            onClick={handleSearchClick}
            className="flex items-center px-3 py-1 border border-gray-400 bg-gradient-to-b from-white to-gray-100 hover:from-gray-50 hover:to-gray-150 mr-2 rounded-sm"
            title="Search"
          >
            <Search size={16} className="text-gray-700 mr-1" />
            <span className="text-sm text-gray-700">Search</span>
          </button>

          {/* Folders button */}
          <button
            onClick={onFoldersClick}
            className="flex items-center px-3 py-1 border border-gray-400 bg-gradient-to-b from-white to-gray-100 hover:from-gray-50 hover:to-gray-150 mr-2 rounded-sm"
            title="Folders"
          >
            <FolderOpen size={16} className="text-gray-700 mr-1" />
            <span className="text-sm text-gray-700">Folders</span>
          </button>

          {/* More options */}
          <button
            className="w-8 h-8 flex items-center justify-center border border-gray-400 bg-gradient-to-b from-white to-gray-100 hover:from-gray-50 hover:to-gray-150 rounded-sm"
            title="Views"
          >
            <MoreHorizontal size={16} className="text-gray-700" />
          </button>

          {/* Custom actions */}
          {customActions && (
            <div className="flex items-center ml-auto">
              {customActions}
            </div>
          )}
        </div>
      )}

      {/* Address Bar */}
      {showAddressBar && (
        <div className="flex items-center px-2 py-2">
          <span className="text-sm text-gray-700 mr-2">Address</span>
          <div className="flex-1 flex items-center bg-white border border-gray-400 rounded-sm">
            {icon && (
              <div className="w-6 h-6 flex items-center justify-center ml-2">
                {renderIcon()}
              </div>
            )}
            <span className="px-2 py-1 text-sm text-gray-800 flex-1">{title}</span>
            <button className="w-6 h-6 flex items-center justify-center bg-gradient-to-b from-white to-gray-100 border-l border-gray-400 hover:from-gray-50 hover:to-gray-150">
              <span className="text-xs text-gray-600">▼</span>
            </button>
          </div>
          <button className="ml-2 px-3 py-1 bg-gradient-to-b from-white to-gray-100 border border-gray-400 rounded-sm hover:from-gray-50 hover:to-gray-150">
            <span className="text-sm text-gray-700">Go</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default WindowsFolderHeader;
