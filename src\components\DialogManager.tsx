import React from 'react';
import { useDialog } from '../contexts/DialogContext';
import { DialogState } from '../hooks/useDialogManager';
import SearchDialog from './SearchDialog';
import ThemeToggle from '@shared/components/ThemeToggle';

interface DialogManagerProps {
  children?: React.ReactNode;
}

/**
 * Dialog manager component that renders all active dialogs
 * Integrates with the Windows XP desktop environment
 */
export const DialogManager: React.FC<DialogManagerProps> = ({ children }) => {
  const { dialogs } = useDialog();

  return (
    <>
      {children}
      
      {/* Render all active dialogs */}
      {dialogs.map((dialog) => (
        <DialogRenderer key={dialog.id} dialog={dialog} />
      ))}
    </>
  );
};

interface DialogRendererProps {
  dialog: DialogState;
}

interface WelcomeDialogProps {
  dialog: DialogState;
  onClose: () => void;
}

/**
 * Welcome dialog component with theme switching functionality
 */
const WelcomeDialog: React.FC<WelcomeDialogProps> = ({ dialog, onClose }) => {

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center animate-fade-in p-4"
      style={{ zIndex: dialog.zIndex }}
      onClick={onClose}
    >
      <div
        className="bg-[#D4E1F4] border-2 border-t-[#769bce] border-l-[#769bce] border-r-[#183969] border-b-[#183969] shadow-2xl w-full max-w-[450px] max-h-[90vh] rounded-lg overflow-hidden animate-window-open"
        onClick={(e) => e.stopPropagation()}
        style={{ fontFamily: 'Tahoma, sans-serif' }}
      >
        {/* Title Bar */}
        <div className="bg-gradient-to-r from-[#2155C4] to-[#4D90FE] text-white font-bold text-sm px-3 py-1 flex justify-between items-center cursor-default">
          <span>Welcome!</span>
          <button
            onClick={onClose}
            className="bg-[#E2614A] hover:bg-[#F07A68] border border-[#8C3B2E] text-white w-5 h-5 flex items-center justify-center rounded-sm text-xs font-mono"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="p-4 text-sm overflow-y-auto max-h-[calc(90vh-3rem)]">
          <div>
            <h3 className="font-bold text-lg mb-2">Welcome to my Windows XP-themed Portfolio!</h3>
            <p className="mb-4">
              You can explore by double-clicking desktop icons or using the Start menu.
            </p>
            <p className="font-semibold mb-2">For quick access:</p>
            {dialog.data?.quickActions && (
              <div className="mt-2 space-y-2 mb-4">
                {dialog.data.quickActions.map((action: any, index: number) => (
                  <button
                    key={index}
                    onClick={() => {
                      action.onClick();
                      onClose();
                    }}
                    className="w-full flex items-center space-x-3 px-3 py-2 bg-white border rounded hover:bg-gray-100 transition-colors text-left"
                  >
                    {action.icon}
                    <span className="font-semibold">{action.label}</span>
                  </button>
                ))}
              </div>
            )}

            {/* Theme Switch Section */}
            <div className="mt-4 pt-4 border-t border-gray-300">
              <p className="mb-2 text-gray-700">
                Prefer a modern web interface?
              </p>
              <div onClick={onClose}>
                <ThemeToggle
                  variant="xp-modern"
                  position="inline"
                  showLabels={true}
                  size="sm"
                  className="text-blue-600 hover:text-blue-800 underline hover:no-underline transition-all duration-200 font-medium"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Individual dialog renderer that handles different dialog types
 */
const DialogRenderer: React.FC<DialogRendererProps> = ({ dialog }) => {
  const { closeDialog, bringToFront } = useDialog();

  const handleClose = () => {
    closeDialog(dialog.id);
  };

  const handleBringToFront = () => {
    bringToFront(dialog.id);
  };

  // Base dialog wrapper with Windows XP styling
  const DialogWrapper: React.FC<{ children: React.ReactNode; title?: string }> = ({ 
    children, 
    title = 'Dialog' 
  }) => (
    <div
      className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center animate-fade-in p-4"
      style={{ zIndex: dialog.zIndex }}
      onClick={handleClose}
    >
      <div
        className="bg-[#D4E1F4] border-2 border-t-[#769bce] border-l-[#769bce] border-r-[#183969] border-b-[#183969] shadow-2xl w-full max-w-[450px] max-h-[90vh] rounded-lg overflow-hidden animate-window-open"
        onClick={(e) => {
          e.stopPropagation();
          handleBringToFront();
        }}
        style={{ fontFamily: 'Tahoma, sans-serif' }}
      >
        {/* Title Bar */}
        <div className="bg-gradient-to-r from-[#2155C4] to-[#4D90FE] text-white font-bold text-sm px-3 py-1 flex justify-between items-center cursor-default">
          <span>{title}</span>
          <button 
            onClick={handleClose} 
            className="bg-[#E2614A] hover:bg-[#F07A68] border border-[#8C3B2E] text-white w-5 h-5 flex items-center justify-center rounded-sm text-xs font-mono"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="p-4 text-sm overflow-y-auto max-h-[calc(90vh-3rem)]">
          {children}
        </div>
      </div>
    </div>
  );

  // Render different dialog types
  switch (dialog.type) {
    case 'welcome':
      return (
        <WelcomeDialog dialog={dialog} onClose={handleClose} />
      );

    case 'notification':
      return (
        <DialogWrapper title={dialog.data?.title || 'Notification'}>
          <div className="flex items-start space-x-3">
            {dialog.data?.icon && (
              <div className="text-2xl">{dialog.data.icon}</div>
            )}
            <div>
              <p>{dialog.data?.message || 'No message provided'}</p>
              {dialog.data?.details && (
                <p className="text-xs text-gray-600 mt-2">{dialog.data.details}</p>
              )}
            </div>
          </div>
        </DialogWrapper>
      );

    case 'confirmation':
      return (
        <DialogWrapper title={dialog.data?.title || 'Confirmation'}>
          <div>
            <p className="mb-4">{dialog.data?.message || 'Are you sure?'}</p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => {
                  dialog.data?.onCancel?.();
                  handleClose();
                }}
                className="px-4 py-2 bg-gray-200 hover:bg-gray-300 border border-gray-400 rounded text-sm"
              >
                {dialog.data?.cancelText || 'Cancel'}
              </button>
              <button
                onClick={() => {
                  dialog.data?.onConfirm?.();
                  handleClose();
                }}
                className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white border border-blue-600 rounded text-sm"
              >
                {dialog.data?.confirmText || 'OK'}
              </button>
            </div>
          </div>
        </DialogWrapper>
      );

    case 'error':
      return (
        <DialogWrapper title="Error">
          <div className="flex items-start space-x-3">
            <div className="text-red-600 text-2xl">❌</div>
            <div>
              <h3 className="font-bold text-red-800 mb-2">
                {dialog.data?.title || 'An error occurred'}
              </h3>
              <p className="text-sm mb-2">{dialog.data?.message || 'Unknown error'}</p>
              {dialog.data?.details && (
                <p className="text-xs text-gray-600">{dialog.data.details}</p>
              )}
            </div>
          </div>
        </DialogWrapper>
      );

    case 'search':
      return (
        <SearchDialog
          isOpen={dialog.isOpen}
          onClose={handleClose}
          onOpenWindow={dialog.data?.onOpenWindow}
          onNavigate={dialog.data?.onNavigate}
        />
      );

    default:
      // Custom dialog type - render data as content
      return (
        <DialogWrapper title={dialog.data?.title || dialog.type}>
          <div>
            {dialog.data?.content || (
              <p>Dialog of type: {dialog.type}</p>
            )}
          </div>
        </DialogWrapper>
      );
  }
};

export default DialogManager;
