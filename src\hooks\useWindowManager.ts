
import { useState, useCallback } from 'react';
import { OpenWindow } from '../types';
import { desktopIcons, portfolioItems } from '../lib/constants';
import { getWindowContent } from '../lib/windowContent';
import { useMobileNavigationSafe } from '../contexts/MobileNavigationContext';
import { NavigationState } from './useMobileNavigation';

export const useWindowManager = (isMobile: boolean) => {
  const [openWindows, setOpenWindows] = useState<OpenWindow[]>([]);
  const [maxZIndex, setMaxZIndex] = useState(1000);
  const mobileNavigation = useMobileNavigationSafe();

  const openWindow = useCallback((iconId: string, iconName: string) => {
    const allIcons = [...desktopIcons, ...portfolioItems];
    const iconData = allIcons.find(i => i.id === iconId);

    // Check for existing window of the same type
    const existingWindow = openWindows.find(w => w.id === iconId);
    if (existingWindow) {
      setOpenWindows(prev => prev.map(w =>
        w.id === iconId
          ? { ...w, isMinimized: false, zIndex: maxZIndex + 1 }
          : w
      ));
      setMaxZIndex(prev => prev + 1);
      return;
    }

    // Single-instance dialog behavior for project-related windows
    // Determine which windows to close based on what we're opening
    let windowsToClose: string[] = [];

    if (iconId === 'projects') {
      // If opening "My Projects", close any open project dialogs
      windowsToClose = openWindows
        .filter(w => w.id.startsWith('project-'))
        .map(w => w.id);
    } else if (iconId.startsWith('project-')) {
      // If opening a project dialog, close "My Projects" and any other project dialogs
      windowsToClose = openWindows
        .filter(w => w.id === 'projects' || (w.id.startsWith('project-') && w.id !== iconId))
        .map(w => w.id);
    }

    // Close conflicting windows if any
    if (windowsToClose.length > 0) {
      setOpenWindows(prev => prev.filter(w => !windowsToClose.includes(w.id)));
    }

    // Calculate z-index for new window
    const newZIndex = maxZIndex + 1;

    const newWindow: OpenWindow = {
      id: iconId,
      title: iconName,
      icon: iconData?.icon || '📄',
      content: getWindowContent(iconId, {
        onOpenWindow: (childIconId: string, childIconName: string) =>
          openWindow(childIconId, childIconName),
        updateWindowSize,
        windowId: iconId
      }),
      isMinimized: false,
      position: isMobile
        ? { x: 0, y: 0 }
        : { x: 200 + openWindows.length * 30, y: 50 + openWindows.length * 30 },
      size: isMobile
        ? { width: globalThis.window.innerWidth, height: globalThis.window.innerHeight - 40 }
        : iconId === 'my_resume'
        ? { width: 1200, height: 800 }
        : iconId === 'my_computer'
        ? { width: 900, height: 800 }
        : iconId === 'recyclebin'
        ? { width: 900, height: 800 }
        : iconId === 'my_music'
        ? { width: 900, height: 800 }
        : iconId === 'my_pictures'
        ? { width: 900, height: 800 }
        : iconId === 'contact'
        ? { width: 500, height: 400 }
        : iconId.startsWith('project-')
        ? { width: 900, height: 800 }
        : { width: 900, height: 600 },
      zIndex: newZIndex,
      isMaximized: !!isMobile,
    };

    setOpenWindows(prev => [...prev, newWindow]);
    setMaxZIndex(prev => Math.max(prev, newZIndex));

    // Track window opening in mobile navigation history
    if (isMobile && mobileNavigation) {
      mobileNavigation.pushState({
        type: 'window',
        windowId: iconId,
        windowName: iconName
      });
    }
  }, [isMobile, mobileNavigation, openWindows, maxZIndex]);

  const closeWindow = useCallback((windowId: string) => {
    setOpenWindows(prev => prev.filter(w => w.id !== windowId));

    // If closing the last window on mobile, return to desktop state
    if (isMobile && mobileNavigation) {
      const remainingWindows = openWindows.filter(w => w.id !== windowId);
      if (remainingWindows.length === 0) {
        mobileNavigation.replaceCurrentState({
          type: 'desktop'
        });
      }
    }
  }, [isMobile, mobileNavigation, openWindows]);

  const minimizeWindow = (windowId: string) => {
    setOpenWindows(prev => prev.map(w => 
      w.id === windowId ? { ...w, isMinimized: true } : w
    ));
  };

  const maximizeWindow = (windowId: string) => {
    if (isMobile) return;
    setOpenWindows(prev => prev.map(w => 
      w.id === windowId 
        ? { 
            ...w,
            originalPosition: w.isMaximized ? w.originalPosition : w.position,
            originalSize: w.isMaximized ? w.originalSize : w.size,
            position: { x: 0, y: 0 }, 
            size: { width: window.innerWidth, height: window.innerHeight - 40 },
            zIndex: maxZIndex + 1,
            isMaximized: true
          } 
        : w
    ));
    setMaxZIndex(prev => prev + 1);
  };

  const restoreWindow = (windowId: string) => {
    if (isMobile) return;
    setOpenWindows(prev => prev.map(w => 
      w.id === windowId && w.originalPosition && w.originalSize
        ? { 
            ...w,
            position: w.originalPosition,
            size: w.originalSize,
            zIndex: maxZIndex + 1,
            isMaximized: false
          } 
        : w
    ));
    setMaxZIndex(prev => prev + 1);
  };

  const bringToFront = (windowId: string) => {
    const windowToFront = openWindows.find(w => w.id === windowId);
    if (windowToFront && windowToFront.zIndex === maxZIndex) {
      return;
    }

    setOpenWindows(prev => prev.map(w =>
      w.id === windowId ? { ...w, zIndex: maxZIndex + 1 } : w
    ));
    setMaxZIndex(prev => prev + 1);
  };

  const updateWindowPosition = (windowId: string, position: { x: number; y: number }) => {
    setOpenWindows(prev => prev.map(w => 
      w.id === windowId ? { ...w, position } : w
    ));
  };

  const updateWindowSize = (windowId: string, size: { width: number; height: number }) => {
    setOpenWindows(prev => prev.map(w => 
      w.id === windowId ? { ...w, size } : w
    ));
  };

  const handleTaskbarClick = (windowId: string) => {
    const window = openWindows.find(w => w.id === windowId);
    if (window?.isMinimized) {
      setOpenWindows(prev => prev.map(w =>
        w.id === windowId
          ? { ...w, isMinimized: false, zIndex: maxZIndex + 1 }
          : w
      ));
      setMaxZIndex(prev => prev + 1);
    } else {
      bringToFront(windowId);
    }
  };

  // Handle navigation back events for mobile
  const handleNavigationBack = useCallback((targetState: NavigationState | null) => {
    if (!isMobile || !targetState) return;

    if (targetState.type === 'desktop') {
      // Close all windows and return to desktop
      setOpenWindows([]);
    } else if (targetState.type === 'window' && targetState.windowId) {
      // Close windows that were opened after the target window
      const targetWindow = openWindows.find(w => w.id === targetState.windowId);
      if (targetWindow) {
        // Bring target window to front and close others opened after it
        setOpenWindows(prev => prev.map(w =>
          w.id === targetState.windowId
            ? { ...w, isMinimized: false, zIndex: maxZIndex + 1 }
            : w
        ));
        setMaxZIndex(prev => prev + 1);
      } else {
        // If target window doesn't exist, reopen it
        openWindow(targetState.windowId, targetState.windowName || targetState.windowId);
      }
    }
  }, [isMobile, openWindows, maxZIndex, openWindow]);

  return {
    openWindows,
    openWindow,
    closeWindow,
    minimizeWindow,
    maximizeWindow,
    restoreWindow,
    bringToFront,
    updateWindowPosition,
    updateWindowSize,
    handleTaskbarClick,
    handleNavigationBack,
  };
};

