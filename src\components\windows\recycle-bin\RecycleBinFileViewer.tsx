import { FileIcon } from '../../../lib/file-icons';
import { RecycleBinFile } from './recycleBinData';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { solarizedlight } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface RecycleBinFileViewerProps {
  file: RecycleBinFile;
}

const getLanguage = (filename: string) => {
  const extension = filename.split('.').pop();
  switch (extension) {
    case 'js':
      return 'javascript';
    case 'html':
      return 'html';
    case 'css':
      return 'css';
    case 'cs':
      return 'csharp';
    default:
      return 'plaintext';
  }
};

const RecycleBinFileViewer = ({ file }: RecycleBinFileViewerProps) => {
  const language = getLanguage(file.name);

  return (
    <>
      <div className="bg-white border-b border-gray-300 p-3 overflow-hidden">
        <div className="flex items-center space-x-3 min-w-0">
          <FileIcon filename={file.name} className="h-6 w-6 text-gray-800 flex-shrink-0" />
          <div className="min-w-0 flex-1">
            <h2 className="font-bold text-gray-800 truncate">{file.name}</h2>
            <p className="text-sm text-gray-600 truncate">
              {file.type} • {file.size} • Modified {file.modified}
            </p>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-auto p-4">
        <div className="bg-white rounded border border-gray-300 p-4 font-mono text-sm whitespace-pre-wrap break-words">
          {language === 'plaintext' ? (
            <pre>{file.content}</pre>
          ) : (
            <SyntaxHighlighter language={language} style={solarizedlight} customStyle={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
              {file.content}
            </SyntaxHighlighter>
          )}
        </div>
      </div>
    </>
  );
};

export default RecycleBinFileViewer;