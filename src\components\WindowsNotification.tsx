import { useState, useEffect } from 'react';
import { X } from 'lucide-react';

interface WindowsNotificationProps {
  title: string;
  message: string;
  icon?: string;
  onClose: () => void;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

const WindowsNotification: React.FC<WindowsNotificationProps> = ({
  title,
  message,
  icon = '',
  onClose,
  autoClose = true,
  autoCloseDelay = 3000
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isClosing, setIsClosing] = useState(false);

  useEffect(() => {
    const showTimer = setTimeout(() => setIsVisible(true), 100);
    
    let autoCloseTimer: NodeJS.Timeout;
    if (autoClose) {
      autoCloseTimer = setTimeout(() => {
        handleClose();
      }, autoCloseDelay);
    }

    return () => {
      clearTimeout(showTimer);
      if (autoCloseTimer) clearTimeout(autoCloseTimer);
    };
  }, [autoClose, autoCloseDelay]);

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      onClose();
    }, 300); // Match animation duration
  };

  return (
    <div
      className={`fixed bottom-12 right-4 w-80 bg-gradient-to-b from-blue-50 to-blue-100 border-2 shadow-2xl z-50 transition-all duration-300 ease-out ${
        isVisible && !isClosing
          ? 'translate-x-0 opacity-100'
          : 'translate-x-full opacity-0'
      }`}
      style={{
        borderTopColor: '#ffffff',
        borderLeftColor: '#ffffff', 
        borderRightColor: '#808080',
        borderBottomColor: '#808080',
        fontFamily: 'Tahoma, sans-serif'
      }}
    >
      {/* Title Bar */}
      <div 
        className="flex items-center justify-between px-2 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs font-bold"
        style={{
          background: 'linear-gradient(to bottom, #0078d4 0%, #106ebe 50%, #005a9e 100%)',
          borderBottom: '1px solid #003d6b'
        }}
      >
        <div className="flex items-center space-x-1">
          <div className="w-4 h-4 bg-white/20 rounded-sm flex items-center justify-center">
            <span className="text-xs">ℹ️</span>
          </div>
          <span>System Notification</span>
        </div>
        <button
          onClick={handleClose}
          className="w-4 h-4 bg-red-500 hover:bg-red-600 border border-red-700 text-white flex items-center justify-center text-xs font-mono transition-colors"
          style={{
            borderTopColor: '#ff6b6b',
            borderLeftColor: '#ff6b6b',
            borderRightColor: '#cc0000',
            borderBottomColor: '#cc0000'
          }}
        >
          <X size={10} />
        </button>
      </div>

      {/* Content */}
      <div className="p-3">
        <div className="flex items-start space-x-3">
          {/* Icon */}
          <div className="flex-shrink-0 w-8 h-8 bg-yellow-400 border border-yellow-600 rounded-sm flex items-center justify-center text-lg shadow-sm">
            {icon}
          </div>
          
          {/* Message Content */}
          <div className="flex-1 min-w-0">
            <h3 className="font-bold text-sm text-gray-800 mb-1">{title}</h3>
            <p className="text-xs text-gray-700 leading-relaxed">{message}</p>
          </div>
        </div>
        
        {/* Bottom border accent */}
        <div className="mt-3 h-1 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full"></div>
      </div>
    </div>
  );
};

export default WindowsNotification;
