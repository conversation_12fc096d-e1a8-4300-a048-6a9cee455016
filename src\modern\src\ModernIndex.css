/* Modern theme specific styles */

/* Ensure smooth scrolling for modern theme */
html {
  scroll-behavior: smooth;
}

/* Custom scroll reveal initial states */
.scroll-reveal-hidden {
  opacity: 0;
  visibility: hidden;
}

/* Ensure ultra-smooth animations with proper initial states */
.animate-fade-in-up {
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-slide-in-left {
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-slide-in-right {
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-scale-in {
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-fade-in {
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Add subtle transform-origin for scale animations */
.animate-scale-in {
  transform-origin: center center;
}

/* Ensure elements start completely hidden */
[style*="opacity: 0"][style*="visibility: hidden"] {
  opacity: 0 !important;
  visibility: hidden !important;
}

/* Enhanced animation utilities for modern theme */
.animate-delay-100 {
  animation-delay: 100ms;
}

.animate-delay-200 {
  animation-delay: 200ms;
}

.animate-delay-300 {
  animation-delay: 300ms;
}

.animate-delay-400 {
  animation-delay: 400ms;
}

.animate-delay-500 {
  animation-delay: 500ms;
}

.animate-delay-600 {
  animation-delay: 600ms;
}

/* Gradient backgrounds for modern theme */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Modern theme specific overrides */
body[data-theme="modern"] {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Smooth transitions for theme switching */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Custom scrollbar for modern theme */
body[data-theme="modern"] ::-webkit-scrollbar {
  width: 8px;
}

body[data-theme="modern"] ::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

body[data-theme="modern"] ::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

body[data-theme="modern"] ::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* Dark mode scrollbar */
body[data-theme="modern"].dark ::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

body[data-theme="modern"].dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

body[data-theme="modern"].dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Performance optimizations and smooth transitions */
.animate-fade-in-up,
.animate-slide-in-left,
.animate-slide-in-right,
.animate-scale-in,
.animate-fade-in {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Ensure elements start in the correct state before animation */
[style*="opacity: 0"][style*="visibility: hidden"] {
  transform: translateY(30px); /* Default for fade-in-up */
}

/* Smooth transition for elements that have completed animation */
.animate-fade-in-up.animation-complete,
.animate-slide-in-left.animation-complete,
.animate-slide-in-right.animation-complete,
.animate-scale-in.animation-complete,
.animate-fade-in.animation-complete {
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in-up,
  .animate-slide-in-left,
  .animate-slide-in-right,
  .animate-scale-in,
  .animate-fade-in {
    animation: none !important;
    opacity: 1 !important;
    transform: none !important;
    visibility: visible !important;
  }

  /* Ensure scroll reveal elements are visible when motion is reduced */
  [style*="opacity: 0"],
  [style*="visibility: hidden"] {
    opacity: 1 !important;
    visibility: visible !important;
  }
}
