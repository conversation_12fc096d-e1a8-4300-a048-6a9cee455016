
import React from 'react';
import { User, Briefcase, FolderOpen, Mail, FileText, Award, Wrench, Settings, BarChart3 } from 'lucide-react';
import WindowsFolderHeader from './WindowsFolderHeader';

interface ControlPanelItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  onClick: () => void;
}

const ControlPanelItem: React.FC<ControlPanelItemProps> = ({ icon, title, description, onClick }) => {
  return (
    <div
      className="flex items-center p-4 bg-white hover:bg-blue-50 border border-gray-300 cursor-pointer transition-colors duration-150 group shadow-sm hover:shadow-md"
      onClick={onClick}
      style={{ minHeight: '80px' }}
    >
      <div className="w-16 h-16 flex items-center justify-center mr-4 bg-gradient-to-br from-blue-100 to-blue-200 rounded group-hover:from-blue-200 group-hover:to-blue-300 transition-colors shadow-inner">
        {icon}
      </div>
      <div className="flex-1">
        <h3 className="font-bold text-blue-800 group-hover:text-blue-900 transition-colors text-base mb-1">{title}</h3>
        <p className="text-sm text-gray-600 group-hover:text-gray-700 transition-colors leading-relaxed">{description}</p>
      </div>
    </div>
  );
};

interface ControlPanelContentProps {
  onOpenWindow?: (id: string, name: string) => void;
}

const ControlPanelContent: React.FC<ControlPanelContentProps> = ({ onOpenWindow }) => {
  const handleItemClick = (windowId: string, windowName: string) => {
    if (onOpenWindow) {
      onOpenWindow(windowId, windowName);
    }
  };

  const controlPanelItems = [
    {
      icon: <User size={32} className="text-blue-600" />,
      title: "About Me & Personal Brand",
      description: "View personal information, background, and professional story",
      windowId: "about",
      windowName: "About Mark Jovet Verano"
    },
    {
      icon: <Briefcase size={32} className="text-green-600" />,
      title: "Skills & Experience",
      description: "Technical skills, programming languages, and professional experience",
      windowId: "skills",
      windowName: "Skills & Experience"
    },
    {
      icon: <FolderOpen size={32} className="text-orange-600" />,
      title: "Project Portfolio",
      description: "Browse and explore my software development projects",
      windowId: "projects",
      windowName: "My Projects"
    },
    {
      icon: <Mail size={32} className="text-purple-600" />,
      title: "Contact Information",
      description: "Get in touch via email, LinkedIn, or other professional channels",
      windowId: "contact",
      windowName: "Contact Info"
    },
    {
      icon: <FileText size={32} className="text-red-600" />,
      title: "Resume/CV",
      description: "View and download my professional resume and curriculum vitae",
      windowId: "my_resume",
      windowName: "Resume - Mark Jovet Verano"
    },
    {
      icon: <Award size={32} className="text-yellow-600" />,
      title: "Certifications & Learning",
      description: "Professional certifications, courses, and continuous learning achievements",
      windowId: "certifications",
      windowName: "Professional Certifications"
    },
    {
      icon: <Wrench size={32} className="text-gray-600" />,
      title: "Technical Skills",
      description: "Detailed breakdown of programming languages, frameworks, and tools",
      windowId: "my_computer",
      windowName: "System Information"
    },
    {
      icon: <BarChart3 size={32} className="text-indigo-600" />,
      title: "Portfolio Analytics",
      description: "View portfolio performance, visitor statistics, and engagement metrics",
      windowId: "portfolio_analytics",
      windowName: "Portfolio Analytics"
    }
  ];

  return (
    <div className="h-full bg-white font-tahoma">
      {/* Header */}
      <WindowsFolderHeader
        title="Control Panel"
        icon={<Settings size={16} className="text-blue-600" />}
        variant="default"
        onBackClick={() => console.log('Back to Desktop')}
        onSearchClick={() => console.log('Search control panel')}
        onFoldersClick={() => console.log('Toggle folders')}
        customActions={
          <button className="px-2 py-1 bg-gradient-to-b from-white to-gray-100 border border-gray-400 rounded-sm hover:from-gray-50 hover:to-gray-150 text-sm text-gray-700">
            View
          </button>
        }
      />

      {/* Content Area */}
      <div className="p-6 overflow-auto bg-gradient-to-br from-blue-100 via-blue-200 to-purple-200" style={{ height: 'calc(100% - 120px)' }}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 max-w-5xl">
          {controlPanelItems.map((item, index) => (
            <ControlPanelItem
              key={index}
              icon={item.icon}
              title={item.title}
              description={item.description}
              onClick={() => handleItemClick(item.windowId, item.windowName)}
            />
          ))}
        </div>

        {/* Footer Note */}
        <div className="mt-8 p-4 bg-white border border-blue-300 rounded shadow-sm max-w-5xl">
          <div className="flex items-start">
            <div className="w-8 h-8 bg-blue-500 rounded mr-3 flex items-center justify-center flex-shrink-0">
              <span className="text-white text-lg">💡</span>
            </div>
            <div>
              <h3 className="font-bold text-blue-800 mb-1">Welcome to my Portfolio Control Panel!</h3>
              <p className="text-sm text-gray-700">
                This Windows XP-inspired interface provides easy access to all aspects of my professional profile.
                Click any category above to explore my background, skills, projects, and contact information.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ControlPanelContent;
